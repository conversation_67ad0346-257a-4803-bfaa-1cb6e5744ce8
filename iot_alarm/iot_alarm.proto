syntax = "proto3";

package iot_alarm;
option go_package="./proto";


message Request {
  string ping = 1;
}

message Response {
  string pong = 1;
}

// 请求jwt配置信息，Secret,Timeout
message JwtConfRequest {

}

message JwtConfResponse {
  string Secret = 1;
  int32 Timeout = 2;
}

service AlarmService { // 常见问题
  rpc Ping(Request) returns(Response);
}


//新增报警规则
message AddAlarmRuleRequest {
  string name=1 ;//报警规则名称
  int32 level=2;//报警等级
  string alarmMemberRule=3;//报警人员规则
  string companyCode = 4;//公司编码
}


message NoticeMemberInfoList{
  repeated NoticeMemberInfo NoticeMemberInfoList=1;
}

message NoticeMemberInfo{
  int32 index = 1;//索引
  int32 type = 2;//通知类型
  int32 interval = 3;//通知间隔
  repeated MemberInfo members = 4;//通知人员
}

message MemberInfo{
  string id=1;//id
  string userId=2;//用户id
  string userName=3;//用户名字
  string phone=4;
  string role=5;
  string position=6;
  bool selected = 7;

}


message AddAlarmRuleResponse {
   bool success = 1;
}

//修改报警规则
message UpdateAlarmRuleRequest {
  int64 id=1;//报警规则id
  string name=2 ;//报警规则名称
  int32 level=3;//报警等级
  string alarmMemberRule=4;//报警人员规则
}

message UpdateAlarmRuleResponse {
   bool success = 1;
}

//删除报警规则
message DeleteAlarmRuleRequest {
  int64 id=1;//报警规则id
}

message DeleteAlarmRuleResponse {
   bool success = 1;
}

//查询报警规则
message QueryAlarmRuleRequest {
  int32 page = 1; // 页码
  int32 pageSize = 2; // 每页数量
  string companyCode = 3;//公司编码
}

message QueryAlarmRuleResponse {
  repeated AlarmRule alarmRuleList = 1; // 报警规则列表
  int32 total = 2; // 总记录数
}

//报警规则
message AlarmRule {
  int64 id = 1; // 报警规则id
  string name = 2; // 报警规则名称
  int32 level = 3; // 报警等级
  string alarmMemberRule = 4; // 报警人员规则
  int32 correlatedEventsCnt = 5; // 绑定事件个数
}

//查询报警信息
message QueryAlarmInfoRequest {
  int32 page = 1; // 页码
  int32 pageSize = 2; // 每页数量
  string companyCode = 3;//公司编码
}

message QueryAlarmInfoResponse {
  repeated AlarmInfo alarmInfoList = 1; // 报警信息列表
  int32 total = 2; // 总记录数
}

message AlarmInfo {
  int64 id = 1; // 报警信息id
  string code = 2;//报警ID
  string eventName = 3;//事件名称
  int32 eventId = 4;//事件id
  int32 alarmRulesId = 5;//告警规则id
  string alarmRules = 6;//告警规则
  int32 level = 7;//告警级别
  int32 status = 8;//状态0未消除，1已派单，2已消除
  string optTime = 9;//消除时间
  string operator = 10;//操作人员名字，为空就是未指派人员
  string companyCode = 11;//公司编码
  string workOrder = 12;//工单号
  string alarmTime = 13;//报警时间
  string workOrderDesc = 14;//派单描述
}


//消除报警信息
message OptAlarmInfoRequest {
  int64 id = 1; // 报警信息id
  string operator = 2;//操作人员名字
}

message OptAlarmInfoResponse {
  bool success = 1;
}

//派单
message DispatchAlarmInfoRequest {
  int64 id = 1; // 报警信息id
  string workOrder = 2;//工单号
  string desc = 3;//描述
  int64 optId = 4;//操作人员id
  string optUUID = 5;//操作人员名字
  string optName = 6;//操作人员名字
  string optRole = 7;//操作人员角色
  string userType = 8;//操作人员类型
}

message DispatchAlarmInfoResponse {
  bool success = 1;
}

enum AlarmInfoStatus{
  AlarmInfoStatusUnDispatched = 0; // 未派单 
  AlarmInfoStatusDispatched = 1; // 已派单
  AlarmInfoStatusOpted = 2; // 已消除
}

//新增报警
message AddAlarmRequest {
  int32 eventId = 1;//事件id
  string eventName = 2;//事件名称
  int32 alarmRulesId = 3;//告警规则id
  string alarmTime = 4;//报警时间
  string devUUID = 5;//设备UUID
}

message AddAlarmResponse {
  bool success = 1;
}

//根据日期查询报警信息
message QueryAlarmInfoByDateRequest {
  string startDate = 1;//开始日期
  string endDate = 2;//结束日期
  string companyCode = 3;//公司编码
}

message QueryAlarmInfoByDateResponse {
  repeated AlarmInfo alarmInfoList = 1; // 报警信息列表
  int32 total = 2; // 总记录数
}

//通知方式
enum NoticeType{
    NoticeTypeNone = 0;
    NoticeTypeInbox = 1; // 站内信
    NoticeTypeShortMessage = 2; // 短信
    NoticeTypePhone = 3; // 外呼
    NoticeTypeEMail = 4; // 邮件
}


service AlarmManagerService {
  // 新增报警规则
  rpc AddAlarmRule(AddAlarmRuleRequest) returns(AddAlarmRuleResponse);  
  // 修改报警规则
  rpc UpdateAlarmRule(UpdateAlarmRuleRequest) returns(UpdateAlarmRuleResponse);
  // 删除报警规则
  rpc DeleteAlarmRule(DeleteAlarmRuleRequest) returns(DeleteAlarmRuleResponse);
  //查询报警规则
  rpc QueryAlarmRule(QueryAlarmRuleRequest) returns(QueryAlarmRuleResponse);

  //查询报警信息
  rpc QueryAlarmInfo(QueryAlarmInfoRequest) returns(QueryAlarmInfoResponse);
  //消除报警信息
  rpc OptAlarmInfo(OptAlarmInfoRequest) returns(OptAlarmInfoResponse);
  //派单
  rpc DispatchAlarmInfo(DispatchAlarmInfoRequest) returns(DispatchAlarmInfoResponse);
  //新增报警
  rpc AddAlarm(AddAlarmRequest) returns(AddAlarmResponse);
  //根据日期查询报警信息
  rpc QueryAlarmInfoByDate(QueryAlarmInfoByDateRequest) returns(QueryAlarmInfoByDateResponse);

}




