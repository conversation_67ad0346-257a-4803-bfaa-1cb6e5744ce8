// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: iot_alarm.proto

package alarmmanagerservice

import (
	"context"

	"git.cdtwzn.com/iot/iot_alarm/proto"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AddAlarmRequest              = proto.AddAlarmRequest
	AddAlarmResponse             = proto.AddAlarmResponse
	AddAlarmRuleRequest          = proto.AddAlarmRuleRequest
	AddAlarmRuleResponse         = proto.AddAlarmRuleResponse
	AlarmInfo                    = proto.AlarmInfo
	AlarmRule                    = proto.AlarmRule
	DeleteAlarmRuleRequest       = proto.DeleteAlarmRuleRequest
	DeleteAlarmRuleResponse      = proto.DeleteAlarmRuleResponse
	DispatchAlarmInfoRequest     = proto.DispatchAlarmInfoRequest
	DispatchAlarmInfoResponse    = proto.DispatchAlarmInfoResponse
	JwtConfRequest               = proto.JwtConfRequest
	JwtConfResponse              = proto.JwtConfResponse
	MemberInfo                   = proto.MemberInfo
	NoticeMemberInfo             = proto.NoticeMemberInfo
	NoticeMemberInfoList         = proto.NoticeMemberInfoList
	OptAlarmInfoRequest          = proto.OptAlarmInfoRequest
	OptAlarmInfoResponse         = proto.OptAlarmInfoResponse
	QueryAlarmInfoByDateRequest  = proto.QueryAlarmInfoByDateRequest
	QueryAlarmInfoByDateResponse = proto.QueryAlarmInfoByDateResponse
	QueryAlarmInfoRequest        = proto.QueryAlarmInfoRequest
	QueryAlarmInfoResponse       = proto.QueryAlarmInfoResponse
	QueryAlarmRuleRequest        = proto.QueryAlarmRuleRequest
	QueryAlarmRuleResponse       = proto.QueryAlarmRuleResponse
	Request                      = proto.Request
	Response                     = proto.Response
	UpdateAlarmRuleRequest       = proto.UpdateAlarmRuleRequest
	UpdateAlarmRuleResponse      = proto.UpdateAlarmRuleResponse

	AlarmManagerService interface {
		// 新增报警规则
		AddAlarmRule(ctx context.Context, in *AddAlarmRuleRequest, opts ...grpc.CallOption) (*AddAlarmRuleResponse, error)
		// 修改报警规则
		UpdateAlarmRule(ctx context.Context, in *UpdateAlarmRuleRequest, opts ...grpc.CallOption) (*UpdateAlarmRuleResponse, error)
		// 删除报警规则
		DeleteAlarmRule(ctx context.Context, in *DeleteAlarmRuleRequest, opts ...grpc.CallOption) (*DeleteAlarmRuleResponse, error)
		// 查询报警规则
		QueryAlarmRule(ctx context.Context, in *QueryAlarmRuleRequest, opts ...grpc.CallOption) (*QueryAlarmRuleResponse, error)
		// 查询报警信息
		QueryAlarmInfo(ctx context.Context, in *QueryAlarmInfoRequest, opts ...grpc.CallOption) (*QueryAlarmInfoResponse, error)
		// 消除报警信息
		OptAlarmInfo(ctx context.Context, in *OptAlarmInfoRequest, opts ...grpc.CallOption) (*OptAlarmInfoResponse, error)
		// 派单
		DispatchAlarmInfo(ctx context.Context, in *DispatchAlarmInfoRequest, opts ...grpc.CallOption) (*DispatchAlarmInfoResponse, error)
		// 新增报警
		AddAlarm(ctx context.Context, in *AddAlarmRequest, opts ...grpc.CallOption) (*AddAlarmResponse, error)
		// 根据日期查询报警信息
		QueryAlarmInfoByDate(ctx context.Context, in *QueryAlarmInfoByDateRequest, opts ...grpc.CallOption) (*QueryAlarmInfoByDateResponse, error)
	}

	defaultAlarmManagerService struct {
		cli zrpc.Client
	}
)

func NewAlarmManagerService(cli zrpc.Client) AlarmManagerService {
	return &defaultAlarmManagerService{
		cli: cli,
	}
}

// 新增报警规则
func (m *defaultAlarmManagerService) AddAlarmRule(ctx context.Context, in *AddAlarmRuleRequest, opts ...grpc.CallOption) (*AddAlarmRuleResponse, error) {
	client := proto.NewAlarmManagerServiceClient(m.cli.Conn())
	return client.AddAlarmRule(ctx, in, opts...)
}

// 修改报警规则
func (m *defaultAlarmManagerService) UpdateAlarmRule(ctx context.Context, in *UpdateAlarmRuleRequest, opts ...grpc.CallOption) (*UpdateAlarmRuleResponse, error) {
	client := proto.NewAlarmManagerServiceClient(m.cli.Conn())
	return client.UpdateAlarmRule(ctx, in, opts...)
}

// 删除报警规则
func (m *defaultAlarmManagerService) DeleteAlarmRule(ctx context.Context, in *DeleteAlarmRuleRequest, opts ...grpc.CallOption) (*DeleteAlarmRuleResponse, error) {
	client := proto.NewAlarmManagerServiceClient(m.cli.Conn())
	return client.DeleteAlarmRule(ctx, in, opts...)
}

// 查询报警规则
func (m *defaultAlarmManagerService) QueryAlarmRule(ctx context.Context, in *QueryAlarmRuleRequest, opts ...grpc.CallOption) (*QueryAlarmRuleResponse, error) {
	client := proto.NewAlarmManagerServiceClient(m.cli.Conn())
	return client.QueryAlarmRule(ctx, in, opts...)
}

// 查询报警信息
func (m *defaultAlarmManagerService) QueryAlarmInfo(ctx context.Context, in *QueryAlarmInfoRequest, opts ...grpc.CallOption) (*QueryAlarmInfoResponse, error) {
	client := proto.NewAlarmManagerServiceClient(m.cli.Conn())
	return client.QueryAlarmInfo(ctx, in, opts...)
}

// 消除报警信息
func (m *defaultAlarmManagerService) OptAlarmInfo(ctx context.Context, in *OptAlarmInfoRequest, opts ...grpc.CallOption) (*OptAlarmInfoResponse, error) {
	client := proto.NewAlarmManagerServiceClient(m.cli.Conn())
	return client.OptAlarmInfo(ctx, in, opts...)
}

// 派单
func (m *defaultAlarmManagerService) DispatchAlarmInfo(ctx context.Context, in *DispatchAlarmInfoRequest, opts ...grpc.CallOption) (*DispatchAlarmInfoResponse, error) {
	client := proto.NewAlarmManagerServiceClient(m.cli.Conn())
	return client.DispatchAlarmInfo(ctx, in, opts...)
}

// 新增报警
func (m *defaultAlarmManagerService) AddAlarm(ctx context.Context, in *AddAlarmRequest, opts ...grpc.CallOption) (*AddAlarmResponse, error) {
	client := proto.NewAlarmManagerServiceClient(m.cli.Conn())
	return client.AddAlarm(ctx, in, opts...)
}

// 根据日期查询报警信息
func (m *defaultAlarmManagerService) QueryAlarmInfoByDate(ctx context.Context, in *QueryAlarmInfoByDateRequest, opts ...grpc.CallOption) (*QueryAlarmInfoByDateResponse, error) {
	client := proto.NewAlarmManagerServiceClient(m.cli.Conn())
	return client.QueryAlarmInfoByDate(ctx, in, opts...)
}
