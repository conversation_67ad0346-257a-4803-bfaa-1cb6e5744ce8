// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: iot_alarm.proto

package alarmservice

import (
	"context"

	"git.cdtwzn.com/iot/iot_alarm/proto"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AddAlarmRequest              = proto.AddAlarmRequest
	AddAlarmResponse             = proto.AddAlarmResponse
	AddAlarmRuleRequest          = proto.AddAlarmRuleRequest
	AddAlarmRuleResponse         = proto.AddAlarmRuleResponse
	AlarmInfo                    = proto.AlarmInfo
	AlarmRule                    = proto.AlarmRule
	DeleteAlarmRuleRequest       = proto.DeleteAlarmRuleRequest
	DeleteAlarmRuleResponse      = proto.DeleteAlarmRuleResponse
	DispatchAlarmInfoRequest     = proto.DispatchAlarmInfoRequest
	DispatchAlarmInfoResponse    = proto.DispatchAlarmInfoResponse
	JwtConfRequest               = proto.JwtConfRequest
	JwtConfResponse              = proto.JwtConfResponse
	MemberInfo                   = proto.MemberInfo
	NoticeMemberInfo             = proto.NoticeMemberInfo
	NoticeMemberInfoList         = proto.NoticeMemberInfoList
	OptAlarmInfoRequest          = proto.OptAlarmInfoRequest
	OptAlarmInfoResponse         = proto.OptAlarmInfoResponse
	QueryAlarmInfoByDateRequest  = proto.QueryAlarmInfoByDateRequest
	QueryAlarmInfoByDateResponse = proto.QueryAlarmInfoByDateResponse
	QueryAlarmInfoRequest        = proto.QueryAlarmInfoRequest
	QueryAlarmInfoResponse       = proto.QueryAlarmInfoResponse
	QueryAlarmRuleRequest        = proto.QueryAlarmRuleRequest
	QueryAlarmRuleResponse       = proto.QueryAlarmRuleResponse
	Request                      = proto.Request
	Response                     = proto.Response
	UpdateAlarmRuleRequest       = proto.UpdateAlarmRuleRequest
	UpdateAlarmRuleResponse      = proto.UpdateAlarmRuleResponse

	AlarmService interface {
		// 常见问题
		Ping(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error)
	}

	defaultAlarmService struct {
		cli zrpc.Client
	}
)

func NewAlarmService(cli zrpc.Client) AlarmService {
	return &defaultAlarmService{
		cli: cli,
	}
}

// 常见问题
func (m *defaultAlarmService) Ping(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error) {
	client := proto.NewAlarmServiceClient(m.cli.Conn())
	return client.Ping(ctx, in, opts...)
}
