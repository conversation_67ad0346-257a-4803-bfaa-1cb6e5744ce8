package svc

import (
	"git.cdtwzn.com/iot/iot_alarm/internal/config"
	"git.cdtwzn.com/iot/iot_alarm/internal/dao/query"
	"git.cdtwzn.com/iot/iot_message/iotmessageclient"

	"git.cdtwzn.com/iot/iot_workorder/iotworkorderclient"

	"git.cdtwzn.com/iot/iot_common/natsc"
	"git.cdtwzn.com/iot/iot_log/pkg/iotlog"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type ServiceContext struct {
	Config config.Config
	// NATS
	Natsc *natsc.Conn
	// Database
	DB *gorm.DB
	// 远程日志
	Rl    iotlog.LogService
	Query *query.Query
	// 工单服务
	WorkOrderRpc iotworkorderclient.IotWorkorder
	// 消息服务
	MessageRpc iotmessageclient.IotMessage
	Redis      *redis.Redis
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config: c,
	}
}

func (s *ServiceContext) Init() error {
	// connect to nats.io
	s.Natsc = natsc.New(natsc.WithAddress(s.Config.NatsConf.Endpoints),
		natsc.WithParams(s.Config.NatsConf.MaxReconnects, s.Config.NatsConf.ReconnectWait, s.Config.NatsConf.RequestTimeout),
		natsc.WithAuth(s.Config.NatsConf.User, s.Config.NatsConf.Pass),
		natsc.WithTLS(s.Config.NatsConf.Tls.Cert, s.Config.NatsConf.Tls.Key, s.Config.NatsConf.Tls.Ca, s.Config.NatsConf.Tls.Verify))

	// 连接到 NATS 服务器
	s.Natsc.Connect()
	// 初始化远程日志服务
	s.Rl = iotlog.NewLogService(s.Config.Name, s.Natsc.Conn)

	//redis
	s.Redis = s.Config.Redis.NewRedis()

	//数据库
	db, err := gorm.Open(mysql.Open(s.Config.DB.DataBaseDsn), &gorm.Config{})
	if err != nil {
		logx.Errorf("Failed to connect to database:%s|", s.Config.DB.DataBaseDsn)
		return err
	}
	s.DB = db

	s.Query = query.Use(s.DB)
	return nil
}

func (s *ServiceContext) WorkOrderService() (iotworkorderclient.IotWorkorder, error) {
	if s.WorkOrderRpc == nil {
		cli, err := zrpc.NewClient(*s.Config.WorkOrderRpcConf)
		if err != nil {
			logx.Errorf("Failed to create work order service client: %v", err)
			return nil, err
		}
		s.WorkOrderRpc = iotworkorderclient.NewIotWorkorder(cli)
	}
	return s.WorkOrderRpc, nil
}

func (s *ServiceContext) MessageService() (iotmessageclient.IotMessage, error) {
	if s.MessageRpc == nil {
		cli, err := zrpc.NewClient(*s.Config.MessageRpcConf)
		if err != nil {
			logx.Errorf("Failed to create message service client: %v", err)
			return nil, err
		}
		s.MessageRpc = iotmessageclient.NewIotMessage(cli)
	}
	return s.MessageRpc, nil
}
