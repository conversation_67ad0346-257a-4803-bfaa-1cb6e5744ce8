package alarmmanagerservicelogic

import (
	"context"
	"encoding/json"

	"git.cdtwzn.com/iot/iot_alarm/internal/dao/model"
	"git.cdtwzn.com/iot/iot_alarm/internal/svc"
	"git.cdtwzn.com/iot/iot_alarm/proto"
	"git.cdtwzn.com/iot/iot_common/utils"
	"git.cdtwzn.com/iot/iot_message/iot_message"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddAlarmLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddAlarmLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddAlarmLogic {
	return &AddAlarmLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 新增报警
func (l *AddAlarmLogic) AddAlarm(in *proto.AddAlarmRequest) (*proto.AddAlarmResponse, error) {
	// todo: add your logic here and delete this line
	logx.Infof("AddAlarm eventName:[%s] eventId:[%d] alarmRulesId:[%d]  alarmTime:[%s]", in.EventName, in.EventId, in.AlarmRulesId, in.AlarmTime)
	q := l.svcCtx.Query

	code, err := utils.UuidV7()
	if err != nil {
		logx.Error(err)
		return nil, err
	}

	alarmRule, err := q.AlarmRule.WithContext(l.ctx).Where(q.AlarmRule.ID.Eq(in.AlarmRulesId)).First()
	if err != nil {
		logx.Error(err)
		return nil, err
	}

	alarmInfo := model.AlarmInfoManager{
		Code:         code,
		EventName:    in.EventName,
		EventID:      in.EventId,
		AlarmRulesID: in.AlarmRulesId,
		AlarmRules:   alarmRule.Name,
		Level:        alarmRule.Level,
		CompanyCode:  alarmRule.CompanyCode,
		AlarmTime:    in.AlarmTime,
		DevUUID:      in.DevUUID,
	}
	err = q.AlarmInfoManager.WithContext(l.ctx).Create(&alarmInfo)
	if err != nil {
		logx.Error(err)
		return nil, err
	}

	//通知人员
	if alarmRule.NoticeMemberRule != "" {
		var noticeMemberInfoList []*proto.NoticeMemberInfo
		err = json.Unmarshal([]byte(alarmRule.NoticeMemberRule), &noticeMemberInfoList)
		if err != nil {
			logx.Infof("noticeMemberInfo: %s", alarmRule.NoticeMemberRule)
			logx.Error(err)
			return nil, err
		}
		for _, noticeMemberInfo := range noticeMemberInfoList {
			if noticeMemberInfo.Type == int32(proto.NoticeType_NoticeTypeInbox) {
				//站内信
				messageService, err := l.svcCtx.MessageService()
				if err != nil {
					logx.Error(err)
					return nil, err
				}

				sendMessageRequest := iot_message.SendMessageRequest{
					Message: &iot_message.Message{
						Title:      "报警通知",
						Content:    "设备" + in.DevUUID + "发生报警",
						Mtype:      iot_message.MessageType_MESSAGE_TYPE_ALARM,
						SenderType: "system",
						SenderId:   0, //系统为0
					},
				}

				for _, member := range noticeMemberInfo.Members {
					sendMessageRequest.Receivers = append(sendMessageRequest.Receivers, &iot_message.Receiver{
						Type: member.Role,
						Id:   member.Id,
					})
				}
				messageService.SendMessage(l.ctx, &sendMessageRequest)
			}

		}

	}
	return &proto.AddAlarmResponse{
		Success: true,
	}, nil
}
