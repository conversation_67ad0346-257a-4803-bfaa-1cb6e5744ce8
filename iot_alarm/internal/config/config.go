package config

import (
	"git.cdtwzn.com/iot/iot_common/config"
	"github.com/zeromicro/go-zero/zrpc"
)

type Config struct {
	zrpc.RpcServerConf
	NatsConf         config.NatsConfig   `json:"Nats" yaml:"Nats"`                         // NATS配置
	DB               config.DatabaseConf `json:"DB" yaml:"DB"`                             // 数据库配置
	WorkOrderRpcConf *zrpc.RpcClientConf `json:"WorkOrderRpcConf" yaml:"WorkOrderRpcConf"` // Work Order RPC client configuration
	MessageRpcConf   *zrpc.RpcClientConf `json:"MessageRpcConf" yaml:"MessageRpcConf"`     // Message RPC client configuration
}
