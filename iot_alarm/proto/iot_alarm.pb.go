// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.19.4
// source: iot_alarm.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AlarmInfoStatus int32

const (
	AlarmInfoStatus_AlarmInfoStatusUnDispatched AlarmInfoStatus = 0 // 未派单
	AlarmInfoStatus_AlarmInfoStatusDispatched   AlarmInfoStatus = 1 // 已派单
	AlarmInfoStatus_AlarmInfoStatusOpted        AlarmInfoStatus = 2 // 已消除
)

// Enum value maps for AlarmInfoStatus.
var (
	AlarmInfoStatus_name = map[int32]string{
		0: "AlarmInfoStatusUnDispatched",
		1: "AlarmInfoStatusDispatched",
		2: "AlarmInfoStatusOpted",
	}
	AlarmInfoStatus_value = map[string]int32{
		"AlarmInfoStatusUnDispatched": 0,
		"AlarmInfoStatusDispatched":   1,
		"AlarmInfoStatusOpted":        2,
	}
)

func (x AlarmInfoStatus) Enum() *AlarmInfoStatus {
	p := new(AlarmInfoStatus)
	*p = x
	return p
}

func (x AlarmInfoStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlarmInfoStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_iot_alarm_proto_enumTypes[0].Descriptor()
}

func (AlarmInfoStatus) Type() protoreflect.EnumType {
	return &file_iot_alarm_proto_enumTypes[0]
}

func (x AlarmInfoStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlarmInfoStatus.Descriptor instead.
func (AlarmInfoStatus) EnumDescriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{0}
}

// 通知方式
type NoticeType int32

const (
	NoticeType_NoticeTypeNone         NoticeType = 0
	NoticeType_NoticeTypeInbox        NoticeType = 1 // 站内信
	NoticeType_NoticeTypeShortMessage NoticeType = 2 // 短信
	NoticeType_NoticeTypePhone        NoticeType = 3 // 外呼
	NoticeType_NoticeTypeEMail        NoticeType = 4 // 邮件
)

// Enum value maps for NoticeType.
var (
	NoticeType_name = map[int32]string{
		0: "NoticeTypeNone",
		1: "NoticeTypeInbox",
		2: "NoticeTypeShortMessage",
		3: "NoticeTypePhone",
		4: "NoticeTypeEMail",
	}
	NoticeType_value = map[string]int32{
		"NoticeTypeNone":         0,
		"NoticeTypeInbox":        1,
		"NoticeTypeShortMessage": 2,
		"NoticeTypePhone":        3,
		"NoticeTypeEMail":        4,
	}
)

func (x NoticeType) Enum() *NoticeType {
	p := new(NoticeType)
	*p = x
	return p
}

func (x NoticeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NoticeType) Descriptor() protoreflect.EnumDescriptor {
	return file_iot_alarm_proto_enumTypes[1].Descriptor()
}

func (NoticeType) Type() protoreflect.EnumType {
	return &file_iot_alarm_proto_enumTypes[1]
}

func (x NoticeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NoticeType.Descriptor instead.
func (NoticeType) EnumDescriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{1}
}

type Request struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ping          string                 `protobuf:"bytes,1,opt,name=ping,proto3" json:"ping,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Request) Reset() {
	*x = Request{}
	mi := &file_iot_alarm_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetPing() string {
	if x != nil {
		return x.Ping
	}
	return ""
}

type Response struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pong          string                 `protobuf:"bytes,1,opt,name=pong,proto3" json:"pong,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Response) Reset() {
	*x = Response{}
	mi := &file_iot_alarm_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{1}
}

func (x *Response) GetPong() string {
	if x != nil {
		return x.Pong
	}
	return ""
}

// 请求jwt配置信息，Secret,Timeout
type JwtConfRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JwtConfRequest) Reset() {
	*x = JwtConfRequest{}
	mi := &file_iot_alarm_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JwtConfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JwtConfRequest) ProtoMessage() {}

func (x *JwtConfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JwtConfRequest.ProtoReflect.Descriptor instead.
func (*JwtConfRequest) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{2}
}

type JwtConfResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Secret        string                 `protobuf:"bytes,1,opt,name=Secret,proto3" json:"Secret,omitempty"`
	Timeout       int32                  `protobuf:"varint,2,opt,name=Timeout,proto3" json:"Timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JwtConfResponse) Reset() {
	*x = JwtConfResponse{}
	mi := &file_iot_alarm_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JwtConfResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JwtConfResponse) ProtoMessage() {}

func (x *JwtConfResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JwtConfResponse.ProtoReflect.Descriptor instead.
func (*JwtConfResponse) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{3}
}

func (x *JwtConfResponse) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *JwtConfResponse) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

// 新增报警规则
type AddAlarmRuleRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Name            string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                       //报警规则名称
	Level           int32                  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`                    //报警等级
	AlarmMemberRule string                 `protobuf:"bytes,3,opt,name=alarmMemberRule,proto3" json:"alarmMemberRule,omitempty"` //报警人员规则
	CompanyCode     string                 `protobuf:"bytes,4,opt,name=companyCode,proto3" json:"companyCode,omitempty"`         //公司编码
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AddAlarmRuleRequest) Reset() {
	*x = AddAlarmRuleRequest{}
	mi := &file_iot_alarm_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAlarmRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAlarmRuleRequest) ProtoMessage() {}

func (x *AddAlarmRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAlarmRuleRequest.ProtoReflect.Descriptor instead.
func (*AddAlarmRuleRequest) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{4}
}

func (x *AddAlarmRuleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddAlarmRuleRequest) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *AddAlarmRuleRequest) GetAlarmMemberRule() string {
	if x != nil {
		return x.AlarmMemberRule
	}
	return ""
}

func (x *AddAlarmRuleRequest) GetCompanyCode() string {
	if x != nil {
		return x.CompanyCode
	}
	return ""
}

type NoticeMemberInfoList struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	NoticeMemberInfoList []*NoticeMemberInfo    `protobuf:"bytes,1,rep,name=NoticeMemberInfoList,proto3" json:"NoticeMemberInfoList,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *NoticeMemberInfoList) Reset() {
	*x = NoticeMemberInfoList{}
	mi := &file_iot_alarm_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoticeMemberInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeMemberInfoList) ProtoMessage() {}

func (x *NoticeMemberInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeMemberInfoList.ProtoReflect.Descriptor instead.
func (*NoticeMemberInfoList) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{5}
}

func (x *NoticeMemberInfoList) GetNoticeMemberInfoList() []*NoticeMemberInfo {
	if x != nil {
		return x.NoticeMemberInfoList
	}
	return nil
}

type NoticeMemberInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Index         int32                  `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`       //索引
	Type          int32                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`         //通知类型
	Interval      int32                  `protobuf:"varint,3,opt,name=interval,proto3" json:"interval,omitempty"` //通知间隔
	Members       []*MemberInfo          `protobuf:"bytes,4,rep,name=members,proto3" json:"members,omitempty"`    //通知人员
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NoticeMemberInfo) Reset() {
	*x = NoticeMemberInfo{}
	mi := &file_iot_alarm_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoticeMemberInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeMemberInfo) ProtoMessage() {}

func (x *NoticeMemberInfo) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeMemberInfo.ProtoReflect.Descriptor instead.
func (*NoticeMemberInfo) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{6}
}

func (x *NoticeMemberInfo) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *NoticeMemberInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *NoticeMemberInfo) GetInterval() int32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *NoticeMemberInfo) GetMembers() []*MemberInfo {
	if x != nil {
		return x.Members
	}
	return nil
}

type MemberInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`             //id
	UserId        string                 `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`     //用户id
	UserName      string                 `protobuf:"bytes,3,opt,name=userName,proto3" json:"userName,omitempty"` //用户名字
	Phone         string                 `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	Role          string                 `protobuf:"bytes,5,opt,name=role,proto3" json:"role,omitempty"`
	Position      string                 `protobuf:"bytes,6,opt,name=position,proto3" json:"position,omitempty"`
	Selected      bool                   `protobuf:"varint,7,opt,name=selected,proto3" json:"selected,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MemberInfo) Reset() {
	*x = MemberInfo{}
	mi := &file_iot_alarm_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemberInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberInfo) ProtoMessage() {}

func (x *MemberInfo) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberInfo.ProtoReflect.Descriptor instead.
func (*MemberInfo) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{7}
}

func (x *MemberInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MemberInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *MemberInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *MemberInfo) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *MemberInfo) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *MemberInfo) GetPosition() string {
	if x != nil {
		return x.Position
	}
	return ""
}

func (x *MemberInfo) GetSelected() bool {
	if x != nil {
		return x.Selected
	}
	return false
}

type AddAlarmRuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddAlarmRuleResponse) Reset() {
	*x = AddAlarmRuleResponse{}
	mi := &file_iot_alarm_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAlarmRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAlarmRuleResponse) ProtoMessage() {}

func (x *AddAlarmRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAlarmRuleResponse.ProtoReflect.Descriptor instead.
func (*AddAlarmRuleResponse) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{8}
}

func (x *AddAlarmRuleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 修改报警规则
type UpdateAlarmRuleRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                          //报警规则id
	Name            string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                       //报警规则名称
	Level           int32                  `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`                    //报警等级
	AlarmMemberRule string                 `protobuf:"bytes,4,opt,name=alarmMemberRule,proto3" json:"alarmMemberRule,omitempty"` //报警人员规则
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UpdateAlarmRuleRequest) Reset() {
	*x = UpdateAlarmRuleRequest{}
	mi := &file_iot_alarm_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAlarmRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAlarmRuleRequest) ProtoMessage() {}

func (x *UpdateAlarmRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAlarmRuleRequest.ProtoReflect.Descriptor instead.
func (*UpdateAlarmRuleRequest) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateAlarmRuleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAlarmRuleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateAlarmRuleRequest) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *UpdateAlarmRuleRequest) GetAlarmMemberRule() string {
	if x != nil {
		return x.AlarmMemberRule
	}
	return ""
}

type UpdateAlarmRuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAlarmRuleResponse) Reset() {
	*x = UpdateAlarmRuleResponse{}
	mi := &file_iot_alarm_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAlarmRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAlarmRuleResponse) ProtoMessage() {}

func (x *UpdateAlarmRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAlarmRuleResponse.ProtoReflect.Descriptor instead.
func (*UpdateAlarmRuleResponse) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateAlarmRuleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 删除报警规则
type DeleteAlarmRuleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //报警规则id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAlarmRuleRequest) Reset() {
	*x = DeleteAlarmRuleRequest{}
	mi := &file_iot_alarm_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAlarmRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAlarmRuleRequest) ProtoMessage() {}

func (x *DeleteAlarmRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAlarmRuleRequest.ProtoReflect.Descriptor instead.
func (*DeleteAlarmRuleRequest) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteAlarmRuleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteAlarmRuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAlarmRuleResponse) Reset() {
	*x = DeleteAlarmRuleResponse{}
	mi := &file_iot_alarm_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAlarmRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAlarmRuleResponse) ProtoMessage() {}

func (x *DeleteAlarmRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAlarmRuleResponse.ProtoReflect.Descriptor instead.
func (*DeleteAlarmRuleResponse) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteAlarmRuleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 查询报警规则
type QueryAlarmRuleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`              // 页码
	PageSize      int32                  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`      // 每页数量
	CompanyCode   string                 `protobuf:"bytes,3,opt,name=companyCode,proto3" json:"companyCode,omitempty"` //公司编码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryAlarmRuleRequest) Reset() {
	*x = QueryAlarmRuleRequest{}
	mi := &file_iot_alarm_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryAlarmRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAlarmRuleRequest) ProtoMessage() {}

func (x *QueryAlarmRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAlarmRuleRequest.ProtoReflect.Descriptor instead.
func (*QueryAlarmRuleRequest) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{13}
}

func (x *QueryAlarmRuleRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *QueryAlarmRuleRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *QueryAlarmRuleRequest) GetCompanyCode() string {
	if x != nil {
		return x.CompanyCode
	}
	return ""
}

type QueryAlarmRuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AlarmRuleList []*AlarmRule           `protobuf:"bytes,1,rep,name=alarmRuleList,proto3" json:"alarmRuleList,omitempty"` // 报警规则列表
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`                // 总记录数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryAlarmRuleResponse) Reset() {
	*x = QueryAlarmRuleResponse{}
	mi := &file_iot_alarm_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryAlarmRuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAlarmRuleResponse) ProtoMessage() {}

func (x *QueryAlarmRuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAlarmRuleResponse.ProtoReflect.Descriptor instead.
func (*QueryAlarmRuleResponse) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{14}
}

func (x *QueryAlarmRuleResponse) GetAlarmRuleList() []*AlarmRule {
	if x != nil {
		return x.AlarmRuleList
	}
	return nil
}

func (x *QueryAlarmRuleResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 报警规则
type AlarmRule struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                   // 报警规则id
	Name                string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                // 报警规则名称
	Level               int32                  `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`                             // 报警等级
	AlarmMemberRule     string                 `protobuf:"bytes,4,opt,name=alarmMemberRule,proto3" json:"alarmMemberRule,omitempty"`          // 报警人员规则
	CorrelatedEventsCnt int32                  `protobuf:"varint,5,opt,name=correlatedEventsCnt,proto3" json:"correlatedEventsCnt,omitempty"` // 绑定事件个数
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *AlarmRule) Reset() {
	*x = AlarmRule{}
	mi := &file_iot_alarm_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlarmRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmRule) ProtoMessage() {}

func (x *AlarmRule) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmRule.ProtoReflect.Descriptor instead.
func (*AlarmRule) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{15}
}

func (x *AlarmRule) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AlarmRule) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AlarmRule) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *AlarmRule) GetAlarmMemberRule() string {
	if x != nil {
		return x.AlarmMemberRule
	}
	return ""
}

func (x *AlarmRule) GetCorrelatedEventsCnt() int32 {
	if x != nil {
		return x.CorrelatedEventsCnt
	}
	return 0
}

// 查询报警信息
type QueryAlarmInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`              // 页码
	PageSize      int32                  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`      // 每页数量
	CompanyCode   string                 `protobuf:"bytes,3,opt,name=companyCode,proto3" json:"companyCode,omitempty"` //公司编码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryAlarmInfoRequest) Reset() {
	*x = QueryAlarmInfoRequest{}
	mi := &file_iot_alarm_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryAlarmInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAlarmInfoRequest) ProtoMessage() {}

func (x *QueryAlarmInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAlarmInfoRequest.ProtoReflect.Descriptor instead.
func (*QueryAlarmInfoRequest) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{16}
}

func (x *QueryAlarmInfoRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *QueryAlarmInfoRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *QueryAlarmInfoRequest) GetCompanyCode() string {
	if x != nil {
		return x.CompanyCode
	}
	return ""
}

type QueryAlarmInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AlarmInfoList []*AlarmInfo           `protobuf:"bytes,1,rep,name=alarmInfoList,proto3" json:"alarmInfoList,omitempty"` // 报警信息列表
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`                // 总记录数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryAlarmInfoResponse) Reset() {
	*x = QueryAlarmInfoResponse{}
	mi := &file_iot_alarm_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryAlarmInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAlarmInfoResponse) ProtoMessage() {}

func (x *QueryAlarmInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAlarmInfoResponse.ProtoReflect.Descriptor instead.
func (*QueryAlarmInfoResponse) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{17}
}

func (x *QueryAlarmInfoResponse) GetAlarmInfoList() []*AlarmInfo {
	if x != nil {
		return x.AlarmInfoList
	}
	return nil
}

func (x *QueryAlarmInfoResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type AlarmInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                       // 报警信息id
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`                    //报警ID
	EventName     string                 `protobuf:"bytes,3,opt,name=eventName,proto3" json:"eventName,omitempty"`          //事件名称
	EventId       int32                  `protobuf:"varint,4,opt,name=eventId,proto3" json:"eventId,omitempty"`             //事件id
	AlarmRulesId  int32                  `protobuf:"varint,5,opt,name=alarmRulesId,proto3" json:"alarmRulesId,omitempty"`   //告警规则id
	AlarmRules    string                 `protobuf:"bytes,6,opt,name=alarmRules,proto3" json:"alarmRules,omitempty"`        //告警规则
	Level         int32                  `protobuf:"varint,7,opt,name=level,proto3" json:"level,omitempty"`                 //告警级别
	Status        int32                  `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`               //状态0未消除，1已派单，2已消除
	OptTime       string                 `protobuf:"bytes,9,opt,name=optTime,proto3" json:"optTime,omitempty"`              //消除时间
	Operator      string                 `protobuf:"bytes,10,opt,name=operator,proto3" json:"operator,omitempty"`           //操作人员名字，为空就是未指派人员
	CompanyCode   string                 `protobuf:"bytes,11,opt,name=companyCode,proto3" json:"companyCode,omitempty"`     //公司编码
	WorkOrder     string                 `protobuf:"bytes,12,opt,name=workOrder,proto3" json:"workOrder,omitempty"`         //工单号
	AlarmTime     string                 `protobuf:"bytes,13,opt,name=alarmTime,proto3" json:"alarmTime,omitempty"`         //报警时间
	WorkOrderDesc string                 `protobuf:"bytes,14,opt,name=workOrderDesc,proto3" json:"workOrderDesc,omitempty"` //派单描述
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlarmInfo) Reset() {
	*x = AlarmInfo{}
	mi := &file_iot_alarm_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlarmInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmInfo) ProtoMessage() {}

func (x *AlarmInfo) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmInfo.ProtoReflect.Descriptor instead.
func (*AlarmInfo) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{18}
}

func (x *AlarmInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AlarmInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *AlarmInfo) GetEventName() string {
	if x != nil {
		return x.EventName
	}
	return ""
}

func (x *AlarmInfo) GetEventId() int32 {
	if x != nil {
		return x.EventId
	}
	return 0
}

func (x *AlarmInfo) GetAlarmRulesId() int32 {
	if x != nil {
		return x.AlarmRulesId
	}
	return 0
}

func (x *AlarmInfo) GetAlarmRules() string {
	if x != nil {
		return x.AlarmRules
	}
	return ""
}

func (x *AlarmInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *AlarmInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AlarmInfo) GetOptTime() string {
	if x != nil {
		return x.OptTime
	}
	return ""
}

func (x *AlarmInfo) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *AlarmInfo) GetCompanyCode() string {
	if x != nil {
		return x.CompanyCode
	}
	return ""
}

func (x *AlarmInfo) GetWorkOrder() string {
	if x != nil {
		return x.WorkOrder
	}
	return ""
}

func (x *AlarmInfo) GetAlarmTime() string {
	if x != nil {
		return x.AlarmTime
	}
	return ""
}

func (x *AlarmInfo) GetWorkOrderDesc() string {
	if x != nil {
		return x.WorkOrderDesc
	}
	return ""
}

// 消除报警信息
type OptAlarmInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`            // 报警信息id
	Operator      string                 `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"` //操作人员名字
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OptAlarmInfoRequest) Reset() {
	*x = OptAlarmInfoRequest{}
	mi := &file_iot_alarm_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptAlarmInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptAlarmInfoRequest) ProtoMessage() {}

func (x *OptAlarmInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptAlarmInfoRequest.ProtoReflect.Descriptor instead.
func (*OptAlarmInfoRequest) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{19}
}

func (x *OptAlarmInfoRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OptAlarmInfoRequest) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

type OptAlarmInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OptAlarmInfoResponse) Reset() {
	*x = OptAlarmInfoResponse{}
	mi := &file_iot_alarm_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptAlarmInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptAlarmInfoResponse) ProtoMessage() {}

func (x *OptAlarmInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptAlarmInfoResponse.ProtoReflect.Descriptor instead.
func (*OptAlarmInfoResponse) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{20}
}

func (x *OptAlarmInfoResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 派单
type DispatchAlarmInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`              // 报警信息id
	WorkOrder     string                 `protobuf:"bytes,2,opt,name=workOrder,proto3" json:"workOrder,omitempty"` //工单号
	Desc          string                 `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`           //描述
	OptId         int64                  `protobuf:"varint,4,opt,name=optId,proto3" json:"optId,omitempty"`        //操作人员id
	OptUUID       string                 `protobuf:"bytes,5,opt,name=optUUID,proto3" json:"optUUID,omitempty"`     //操作人员名字
	OptName       string                 `protobuf:"bytes,6,opt,name=optName,proto3" json:"optName,omitempty"`     //操作人员名字
	OptRole       string                 `protobuf:"bytes,7,opt,name=optRole,proto3" json:"optRole,omitempty"`     //操作人员角色
	UserType      string                 `protobuf:"bytes,8,opt,name=userType,proto3" json:"userType,omitempty"`   //操作人员类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DispatchAlarmInfoRequest) Reset() {
	*x = DispatchAlarmInfoRequest{}
	mi := &file_iot_alarm_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DispatchAlarmInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatchAlarmInfoRequest) ProtoMessage() {}

func (x *DispatchAlarmInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatchAlarmInfoRequest.ProtoReflect.Descriptor instead.
func (*DispatchAlarmInfoRequest) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{21}
}

func (x *DispatchAlarmInfoRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DispatchAlarmInfoRequest) GetWorkOrder() string {
	if x != nil {
		return x.WorkOrder
	}
	return ""
}

func (x *DispatchAlarmInfoRequest) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DispatchAlarmInfoRequest) GetOptId() int64 {
	if x != nil {
		return x.OptId
	}
	return 0
}

func (x *DispatchAlarmInfoRequest) GetOptUUID() string {
	if x != nil {
		return x.OptUUID
	}
	return ""
}

func (x *DispatchAlarmInfoRequest) GetOptName() string {
	if x != nil {
		return x.OptName
	}
	return ""
}

func (x *DispatchAlarmInfoRequest) GetOptRole() string {
	if x != nil {
		return x.OptRole
	}
	return ""
}

func (x *DispatchAlarmInfoRequest) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

type DispatchAlarmInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DispatchAlarmInfoResponse) Reset() {
	*x = DispatchAlarmInfoResponse{}
	mi := &file_iot_alarm_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DispatchAlarmInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatchAlarmInfoResponse) ProtoMessage() {}

func (x *DispatchAlarmInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatchAlarmInfoResponse.ProtoReflect.Descriptor instead.
func (*DispatchAlarmInfoResponse) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{22}
}

func (x *DispatchAlarmInfoResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 新增报警
type AddAlarmRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EventId       int32                  `protobuf:"varint,1,opt,name=eventId,proto3" json:"eventId,omitempty"`           //事件id
	EventName     string                 `protobuf:"bytes,2,opt,name=eventName,proto3" json:"eventName,omitempty"`        //事件名称
	AlarmRulesId  int32                  `protobuf:"varint,3,opt,name=alarmRulesId,proto3" json:"alarmRulesId,omitempty"` //告警规则id
	AlarmTime     string                 `protobuf:"bytes,4,opt,name=alarmTime,proto3" json:"alarmTime,omitempty"`        //报警时间
	DevUUID       string                 `protobuf:"bytes,5,opt,name=devUUID,proto3" json:"devUUID,omitempty"`            //设备UUID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddAlarmRequest) Reset() {
	*x = AddAlarmRequest{}
	mi := &file_iot_alarm_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAlarmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAlarmRequest) ProtoMessage() {}

func (x *AddAlarmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAlarmRequest.ProtoReflect.Descriptor instead.
func (*AddAlarmRequest) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{23}
}

func (x *AddAlarmRequest) GetEventId() int32 {
	if x != nil {
		return x.EventId
	}
	return 0
}

func (x *AddAlarmRequest) GetEventName() string {
	if x != nil {
		return x.EventName
	}
	return ""
}

func (x *AddAlarmRequest) GetAlarmRulesId() int32 {
	if x != nil {
		return x.AlarmRulesId
	}
	return 0
}

func (x *AddAlarmRequest) GetAlarmTime() string {
	if x != nil {
		return x.AlarmTime
	}
	return ""
}

func (x *AddAlarmRequest) GetDevUUID() string {
	if x != nil {
		return x.DevUUID
	}
	return ""
}

type AddAlarmResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddAlarmResponse) Reset() {
	*x = AddAlarmResponse{}
	mi := &file_iot_alarm_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddAlarmResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAlarmResponse) ProtoMessage() {}

func (x *AddAlarmResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAlarmResponse.ProtoReflect.Descriptor instead.
func (*AddAlarmResponse) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{24}
}

func (x *AddAlarmResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 根据日期查询报警信息
type QueryAlarmInfoByDateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartDate     string                 `protobuf:"bytes,1,opt,name=startDate,proto3" json:"startDate,omitempty"`     //开始日期
	EndDate       string                 `protobuf:"bytes,2,opt,name=endDate,proto3" json:"endDate,omitempty"`         //结束日期
	CompanyCode   string                 `protobuf:"bytes,3,opt,name=companyCode,proto3" json:"companyCode,omitempty"` //公司编码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryAlarmInfoByDateRequest) Reset() {
	*x = QueryAlarmInfoByDateRequest{}
	mi := &file_iot_alarm_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryAlarmInfoByDateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAlarmInfoByDateRequest) ProtoMessage() {}

func (x *QueryAlarmInfoByDateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAlarmInfoByDateRequest.ProtoReflect.Descriptor instead.
func (*QueryAlarmInfoByDateRequest) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{25}
}

func (x *QueryAlarmInfoByDateRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *QueryAlarmInfoByDateRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *QueryAlarmInfoByDateRequest) GetCompanyCode() string {
	if x != nil {
		return x.CompanyCode
	}
	return ""
}

type QueryAlarmInfoByDateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AlarmInfoList []*AlarmInfo           `protobuf:"bytes,1,rep,name=alarmInfoList,proto3" json:"alarmInfoList,omitempty"` // 报警信息列表
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`                // 总记录数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryAlarmInfoByDateResponse) Reset() {
	*x = QueryAlarmInfoByDateResponse{}
	mi := &file_iot_alarm_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryAlarmInfoByDateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAlarmInfoByDateResponse) ProtoMessage() {}

func (x *QueryAlarmInfoByDateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_iot_alarm_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAlarmInfoByDateResponse.ProtoReflect.Descriptor instead.
func (*QueryAlarmInfoByDateResponse) Descriptor() ([]byte, []int) {
	return file_iot_alarm_proto_rawDescGZIP(), []int{26}
}

func (x *QueryAlarmInfoByDateResponse) GetAlarmInfoList() []*AlarmInfo {
	if x != nil {
		return x.AlarmInfoList
	}
	return nil
}

func (x *QueryAlarmInfoByDateResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_iot_alarm_proto protoreflect.FileDescriptor

const file_iot_alarm_proto_rawDesc = "" +
	"\n" +
	"\x0fiot_alarm.proto\x12\tiot_alarm\"\x1d\n" +
	"\aRequest\x12\x12\n" +
	"\x04ping\x18\x01 \x01(\tR\x04ping\"\x1e\n" +
	"\bResponse\x12\x12\n" +
	"\x04pong\x18\x01 \x01(\tR\x04pong\"\x10\n" +
	"\x0eJwtConfRequest\"C\n" +
	"\x0fJwtConfResponse\x12\x16\n" +
	"\x06Secret\x18\x01 \x01(\tR\x06Secret\x12\x18\n" +
	"\aTimeout\x18\x02 \x01(\x05R\aTimeout\"\x8b\x01\n" +
	"\x13AddAlarmRuleRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05level\x18\x02 \x01(\x05R\x05level\x12(\n" +
	"\x0falarmMemberRule\x18\x03 \x01(\tR\x0falarmMemberRule\x12 \n" +
	"\vcompanyCode\x18\x04 \x01(\tR\vcompanyCode\"g\n" +
	"\x14NoticeMemberInfoList\x12O\n" +
	"\x14NoticeMemberInfoList\x18\x01 \x03(\v2\x1b.iot_alarm.NoticeMemberInfoR\x14NoticeMemberInfoList\"\x89\x01\n" +
	"\x10NoticeMemberInfo\x12\x14\n" +
	"\x05index\x18\x01 \x01(\x05R\x05index\x12\x12\n" +
	"\x04type\x18\x02 \x01(\x05R\x04type\x12\x1a\n" +
	"\binterval\x18\x03 \x01(\x05R\binterval\x12/\n" +
	"\amembers\x18\x04 \x03(\v2\x15.iot_alarm.MemberInfoR\amembers\"\xb2\x01\n" +
	"\n" +
	"MemberInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\tR\x06userId\x12\x1a\n" +
	"\buserName\x18\x03 \x01(\tR\buserName\x12\x14\n" +
	"\x05phone\x18\x04 \x01(\tR\x05phone\x12\x12\n" +
	"\x04role\x18\x05 \x01(\tR\x04role\x12\x1a\n" +
	"\bposition\x18\x06 \x01(\tR\bposition\x12\x1a\n" +
	"\bselected\x18\a \x01(\bR\bselected\"0\n" +
	"\x14AddAlarmRuleResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"|\n" +
	"\x16UpdateAlarmRuleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05level\x18\x03 \x01(\x05R\x05level\x12(\n" +
	"\x0falarmMemberRule\x18\x04 \x01(\tR\x0falarmMemberRule\"3\n" +
	"\x17UpdateAlarmRuleResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"(\n" +
	"\x16DeleteAlarmRuleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"3\n" +
	"\x17DeleteAlarmRuleResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"i\n" +
	"\x15QueryAlarmRuleRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1a\n" +
	"\bpageSize\x18\x02 \x01(\x05R\bpageSize\x12 \n" +
	"\vcompanyCode\x18\x03 \x01(\tR\vcompanyCode\"j\n" +
	"\x16QueryAlarmRuleResponse\x12:\n" +
	"\ralarmRuleList\x18\x01 \x03(\v2\x14.iot_alarm.AlarmRuleR\ralarmRuleList\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"\xa1\x01\n" +
	"\tAlarmRule\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05level\x18\x03 \x01(\x05R\x05level\x12(\n" +
	"\x0falarmMemberRule\x18\x04 \x01(\tR\x0falarmMemberRule\x120\n" +
	"\x13correlatedEventsCnt\x18\x05 \x01(\x05R\x13correlatedEventsCnt\"i\n" +
	"\x15QueryAlarmInfoRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1a\n" +
	"\bpageSize\x18\x02 \x01(\x05R\bpageSize\x12 \n" +
	"\vcompanyCode\x18\x03 \x01(\tR\vcompanyCode\"j\n" +
	"\x16QueryAlarmInfoResponse\x12:\n" +
	"\ralarmInfoList\x18\x01 \x03(\v2\x14.iot_alarm.AlarmInfoR\ralarmInfoList\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"\x93\x03\n" +
	"\tAlarmInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x1c\n" +
	"\teventName\x18\x03 \x01(\tR\teventName\x12\x18\n" +
	"\aeventId\x18\x04 \x01(\x05R\aeventId\x12\"\n" +
	"\falarmRulesId\x18\x05 \x01(\x05R\falarmRulesId\x12\x1e\n" +
	"\n" +
	"alarmRules\x18\x06 \x01(\tR\n" +
	"alarmRules\x12\x14\n" +
	"\x05level\x18\a \x01(\x05R\x05level\x12\x16\n" +
	"\x06status\x18\b \x01(\x05R\x06status\x12\x18\n" +
	"\aoptTime\x18\t \x01(\tR\aoptTime\x12\x1a\n" +
	"\boperator\x18\n" +
	" \x01(\tR\boperator\x12 \n" +
	"\vcompanyCode\x18\v \x01(\tR\vcompanyCode\x12\x1c\n" +
	"\tworkOrder\x18\f \x01(\tR\tworkOrder\x12\x1c\n" +
	"\talarmTime\x18\r \x01(\tR\talarmTime\x12$\n" +
	"\rworkOrderDesc\x18\x0e \x01(\tR\rworkOrderDesc\"A\n" +
	"\x13OptAlarmInfoRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1a\n" +
	"\boperator\x18\x02 \x01(\tR\boperator\"0\n" +
	"\x14OptAlarmInfoResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"\xdc\x01\n" +
	"\x18DispatchAlarmInfoRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1c\n" +
	"\tworkOrder\x18\x02 \x01(\tR\tworkOrder\x12\x12\n" +
	"\x04desc\x18\x03 \x01(\tR\x04desc\x12\x14\n" +
	"\x05optId\x18\x04 \x01(\x03R\x05optId\x12\x18\n" +
	"\aoptUUID\x18\x05 \x01(\tR\aoptUUID\x12\x18\n" +
	"\aoptName\x18\x06 \x01(\tR\aoptName\x12\x18\n" +
	"\aoptRole\x18\a \x01(\tR\aoptRole\x12\x1a\n" +
	"\buserType\x18\b \x01(\tR\buserType\"5\n" +
	"\x19DispatchAlarmInfoResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"\xa5\x01\n" +
	"\x0fAddAlarmRequest\x12\x18\n" +
	"\aeventId\x18\x01 \x01(\x05R\aeventId\x12\x1c\n" +
	"\teventName\x18\x02 \x01(\tR\teventName\x12\"\n" +
	"\falarmRulesId\x18\x03 \x01(\x05R\falarmRulesId\x12\x1c\n" +
	"\talarmTime\x18\x04 \x01(\tR\talarmTime\x12\x18\n" +
	"\adevUUID\x18\x05 \x01(\tR\adevUUID\",\n" +
	"\x10AddAlarmResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"w\n" +
	"\x1bQueryAlarmInfoByDateRequest\x12\x1c\n" +
	"\tstartDate\x18\x01 \x01(\tR\tstartDate\x12\x18\n" +
	"\aendDate\x18\x02 \x01(\tR\aendDate\x12 \n" +
	"\vcompanyCode\x18\x03 \x01(\tR\vcompanyCode\"p\n" +
	"\x1cQueryAlarmInfoByDateResponse\x12:\n" +
	"\ralarmInfoList\x18\x01 \x03(\v2\x14.iot_alarm.AlarmInfoR\ralarmInfoList\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total*k\n" +
	"\x0fAlarmInfoStatus\x12\x1f\n" +
	"\x1bAlarmInfoStatusUnDispatched\x10\x00\x12\x1d\n" +
	"\x19AlarmInfoStatusDispatched\x10\x01\x12\x18\n" +
	"\x14AlarmInfoStatusOpted\x10\x02*{\n" +
	"\n" +
	"NoticeType\x12\x12\n" +
	"\x0eNoticeTypeNone\x10\x00\x12\x13\n" +
	"\x0fNoticeTypeInbox\x10\x01\x12\x1a\n" +
	"\x16NoticeTypeShortMessage\x10\x02\x12\x13\n" +
	"\x0fNoticeTypePhone\x10\x03\x12\x13\n" +
	"\x0fNoticeTypeEMail\x10\x042?\n" +
	"\fAlarmService\x12/\n" +
	"\x04Ping\x12\x12.iot_alarm.Request\x1a\x13.iot_alarm.Response2\xa7\x06\n" +
	"\x13AlarmManagerService\x12O\n" +
	"\fAddAlarmRule\x12\x1e.iot_alarm.AddAlarmRuleRequest\x1a\x1f.iot_alarm.AddAlarmRuleResponse\x12X\n" +
	"\x0fUpdateAlarmRule\x12!.iot_alarm.UpdateAlarmRuleRequest\x1a\".iot_alarm.UpdateAlarmRuleResponse\x12X\n" +
	"\x0fDeleteAlarmRule\x12!.iot_alarm.DeleteAlarmRuleRequest\x1a\".iot_alarm.DeleteAlarmRuleResponse\x12U\n" +
	"\x0eQueryAlarmRule\x12 .iot_alarm.QueryAlarmRuleRequest\x1a!.iot_alarm.QueryAlarmRuleResponse\x12U\n" +
	"\x0eQueryAlarmInfo\x12 .iot_alarm.QueryAlarmInfoRequest\x1a!.iot_alarm.QueryAlarmInfoResponse\x12O\n" +
	"\fOptAlarmInfo\x12\x1e.iot_alarm.OptAlarmInfoRequest\x1a\x1f.iot_alarm.OptAlarmInfoResponse\x12^\n" +
	"\x11DispatchAlarmInfo\x12#.iot_alarm.DispatchAlarmInfoRequest\x1a$.iot_alarm.DispatchAlarmInfoResponse\x12C\n" +
	"\bAddAlarm\x12\x1a.iot_alarm.AddAlarmRequest\x1a\x1b.iot_alarm.AddAlarmResponse\x12g\n" +
	"\x14QueryAlarmInfoByDate\x12&.iot_alarm.QueryAlarmInfoByDateRequest\x1a'.iot_alarm.QueryAlarmInfoByDateResponseB\tZ\a./protob\x06proto3"

var (
	file_iot_alarm_proto_rawDescOnce sync.Once
	file_iot_alarm_proto_rawDescData []byte
)

func file_iot_alarm_proto_rawDescGZIP() []byte {
	file_iot_alarm_proto_rawDescOnce.Do(func() {
		file_iot_alarm_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_iot_alarm_proto_rawDesc), len(file_iot_alarm_proto_rawDesc)))
	})
	return file_iot_alarm_proto_rawDescData
}

var file_iot_alarm_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_iot_alarm_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_iot_alarm_proto_goTypes = []any{
	(AlarmInfoStatus)(0),                 // 0: iot_alarm.AlarmInfoStatus
	(NoticeType)(0),                      // 1: iot_alarm.NoticeType
	(*Request)(nil),                      // 2: iot_alarm.Request
	(*Response)(nil),                     // 3: iot_alarm.Response
	(*JwtConfRequest)(nil),               // 4: iot_alarm.JwtConfRequest
	(*JwtConfResponse)(nil),              // 5: iot_alarm.JwtConfResponse
	(*AddAlarmRuleRequest)(nil),          // 6: iot_alarm.AddAlarmRuleRequest
	(*NoticeMemberInfoList)(nil),         // 7: iot_alarm.NoticeMemberInfoList
	(*NoticeMemberInfo)(nil),             // 8: iot_alarm.NoticeMemberInfo
	(*MemberInfo)(nil),                   // 9: iot_alarm.MemberInfo
	(*AddAlarmRuleResponse)(nil),         // 10: iot_alarm.AddAlarmRuleResponse
	(*UpdateAlarmRuleRequest)(nil),       // 11: iot_alarm.UpdateAlarmRuleRequest
	(*UpdateAlarmRuleResponse)(nil),      // 12: iot_alarm.UpdateAlarmRuleResponse
	(*DeleteAlarmRuleRequest)(nil),       // 13: iot_alarm.DeleteAlarmRuleRequest
	(*DeleteAlarmRuleResponse)(nil),      // 14: iot_alarm.DeleteAlarmRuleResponse
	(*QueryAlarmRuleRequest)(nil),        // 15: iot_alarm.QueryAlarmRuleRequest
	(*QueryAlarmRuleResponse)(nil),       // 16: iot_alarm.QueryAlarmRuleResponse
	(*AlarmRule)(nil),                    // 17: iot_alarm.AlarmRule
	(*QueryAlarmInfoRequest)(nil),        // 18: iot_alarm.QueryAlarmInfoRequest
	(*QueryAlarmInfoResponse)(nil),       // 19: iot_alarm.QueryAlarmInfoResponse
	(*AlarmInfo)(nil),                    // 20: iot_alarm.AlarmInfo
	(*OptAlarmInfoRequest)(nil),          // 21: iot_alarm.OptAlarmInfoRequest
	(*OptAlarmInfoResponse)(nil),         // 22: iot_alarm.OptAlarmInfoResponse
	(*DispatchAlarmInfoRequest)(nil),     // 23: iot_alarm.DispatchAlarmInfoRequest
	(*DispatchAlarmInfoResponse)(nil),    // 24: iot_alarm.DispatchAlarmInfoResponse
	(*AddAlarmRequest)(nil),              // 25: iot_alarm.AddAlarmRequest
	(*AddAlarmResponse)(nil),             // 26: iot_alarm.AddAlarmResponse
	(*QueryAlarmInfoByDateRequest)(nil),  // 27: iot_alarm.QueryAlarmInfoByDateRequest
	(*QueryAlarmInfoByDateResponse)(nil), // 28: iot_alarm.QueryAlarmInfoByDateResponse
}
var file_iot_alarm_proto_depIdxs = []int32{
	8,  // 0: iot_alarm.NoticeMemberInfoList.NoticeMemberInfoList:type_name -> iot_alarm.NoticeMemberInfo
	9,  // 1: iot_alarm.NoticeMemberInfo.members:type_name -> iot_alarm.MemberInfo
	17, // 2: iot_alarm.QueryAlarmRuleResponse.alarmRuleList:type_name -> iot_alarm.AlarmRule
	20, // 3: iot_alarm.QueryAlarmInfoResponse.alarmInfoList:type_name -> iot_alarm.AlarmInfo
	20, // 4: iot_alarm.QueryAlarmInfoByDateResponse.alarmInfoList:type_name -> iot_alarm.AlarmInfo
	2,  // 5: iot_alarm.AlarmService.Ping:input_type -> iot_alarm.Request
	6,  // 6: iot_alarm.AlarmManagerService.AddAlarmRule:input_type -> iot_alarm.AddAlarmRuleRequest
	11, // 7: iot_alarm.AlarmManagerService.UpdateAlarmRule:input_type -> iot_alarm.UpdateAlarmRuleRequest
	13, // 8: iot_alarm.AlarmManagerService.DeleteAlarmRule:input_type -> iot_alarm.DeleteAlarmRuleRequest
	15, // 9: iot_alarm.AlarmManagerService.QueryAlarmRule:input_type -> iot_alarm.QueryAlarmRuleRequest
	18, // 10: iot_alarm.AlarmManagerService.QueryAlarmInfo:input_type -> iot_alarm.QueryAlarmInfoRequest
	21, // 11: iot_alarm.AlarmManagerService.OptAlarmInfo:input_type -> iot_alarm.OptAlarmInfoRequest
	23, // 12: iot_alarm.AlarmManagerService.DispatchAlarmInfo:input_type -> iot_alarm.DispatchAlarmInfoRequest
	25, // 13: iot_alarm.AlarmManagerService.AddAlarm:input_type -> iot_alarm.AddAlarmRequest
	27, // 14: iot_alarm.AlarmManagerService.QueryAlarmInfoByDate:input_type -> iot_alarm.QueryAlarmInfoByDateRequest
	3,  // 15: iot_alarm.AlarmService.Ping:output_type -> iot_alarm.Response
	10, // 16: iot_alarm.AlarmManagerService.AddAlarmRule:output_type -> iot_alarm.AddAlarmRuleResponse
	12, // 17: iot_alarm.AlarmManagerService.UpdateAlarmRule:output_type -> iot_alarm.UpdateAlarmRuleResponse
	14, // 18: iot_alarm.AlarmManagerService.DeleteAlarmRule:output_type -> iot_alarm.DeleteAlarmRuleResponse
	16, // 19: iot_alarm.AlarmManagerService.QueryAlarmRule:output_type -> iot_alarm.QueryAlarmRuleResponse
	19, // 20: iot_alarm.AlarmManagerService.QueryAlarmInfo:output_type -> iot_alarm.QueryAlarmInfoResponse
	22, // 21: iot_alarm.AlarmManagerService.OptAlarmInfo:output_type -> iot_alarm.OptAlarmInfoResponse
	24, // 22: iot_alarm.AlarmManagerService.DispatchAlarmInfo:output_type -> iot_alarm.DispatchAlarmInfoResponse
	26, // 23: iot_alarm.AlarmManagerService.AddAlarm:output_type -> iot_alarm.AddAlarmResponse
	28, // 24: iot_alarm.AlarmManagerService.QueryAlarmInfoByDate:output_type -> iot_alarm.QueryAlarmInfoByDateResponse
	15, // [15:25] is the sub-list for method output_type
	5,  // [5:15] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_iot_alarm_proto_init() }
func file_iot_alarm_proto_init() {
	if File_iot_alarm_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_iot_alarm_proto_rawDesc), len(file_iot_alarm_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_iot_alarm_proto_goTypes,
		DependencyIndexes: file_iot_alarm_proto_depIdxs,
		EnumInfos:         file_iot_alarm_proto_enumTypes,
		MessageInfos:      file_iot_alarm_proto_msgTypes,
	}.Build()
	File_iot_alarm_proto = out.File
	file_iot_alarm_proto_goTypes = nil
	file_iot_alarm_proto_depIdxs = nil
}
