Name: iotalarm.rpc
ListenOn: 0.0.0.0:6060
Mode: dev # Mode: dev|test|rt|pre|pro
Etcd:
  Hosts:
  - 127.0.0.1:2379
  Key: iotalarm.rpc

DB:
  DataBaseDsn: root:123456@tcp(************:3306)/iot_alarm?charset=utf8mb4&parseTime=true&loc=Local

Redis:
  Host: 127.0.0.1:6379
  Pass: ""
  Db: 0
  Key: "iotalarm.rpc"

Log:
  Level: debug
  Encoding: plain
  Stat: false

Nats:
  endpoints: nats://127.0.0.1:4222
  user: twznedge
  pass: 3f0TDZ+W<55k9XzJHtle_dHs9Rm-TOc*531EAE&&zh&aN2-7oBBJpN|7YT=G=?Ng
  max_reconnects: 100
  reconnect_wait: 10 #s
  request_timeout: 10 #s
  tls:
    verify: true
    cert: /opt/workspace/iot/design/cert/client/client.pem
    key: /opt/workspace/iot/design/cert/client/client.key
    ca: /opt/workspace/iot/design/cert/ca/ca.pem

WorkOrderRpcConf:
  Etcd:
    Hosts:
    - 127.0.0.1:2379
    Key: iotworkorder.rpc

MessageRpcConf:
  Etcd:
    Hosts:
    - 127.0.0.1:2379
    Key: iotmessage.rpc