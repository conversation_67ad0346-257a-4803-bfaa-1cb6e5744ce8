package usertypes

const (
	UserTypeSystem   = "system"
	UserTypeUser     = "user"
	UserTypeAdmin    = "admin"
	UserTypeCustomer = "customer"
	UserTypeMember   = "member"
)

func CheckUserType(t string) bool {
	switch t {
	case UserTypeSystem, UserTypeUser, UserTypeAdmin, UserTypeCustomer, UserTypeMember:
		return true
	default:
		return false
	}
}

// 是否是管理员类型
func IsAdminType(roles []string) bool {
	for _, role := range roles {
		if role == UserTypeAdmin {
			return true
		}
	}
	return false
}

// 是否是客户类型
func IsCustomerType(roles []string) bool {
	for _, role := range roles {
		if role == UserTypeCustomer {
			return true
		}
	}
	return false
}

// 是否是客户成员类型
func IsMemberType(roles []string) bool {
	for _, role := range roles {
		if role == UserTypeMember {
			return true
		}
	}
	return false
}

// 是否是系统类型
func IsSystemType(roles []string) bool {
	for _, role := range roles {
		if role == UserTypeSystem {
			return true
		}
	}
	return false
}

// 是否是用户类型
func IsUserType(roles []string) bool {
	for _, role := range roles {
		if role == UserTypeUser {
			return true
		}
	}
	return false
}
