package version

import (
	"encoding/json"
	"fmt"
	"runtime"
)

var (
	gitVersion = ""
	gitCommit  = ""
	buildDate  = ""
)

type Version struct {
	GitVersion string `json:"gitVersion"`
	GitCommit  string `json:"gitCommit"`
	BuildDate  string `json:"buildDate"`
	GoVersion  string `json:"goVersion"`
	Compiler   string `json:"compiler"`
	Platform   string `json:"platform"`
}

func (v Version) String() string {
	bs, err := json.MarshalIndent(v, "", " ")
	if err != nil {
		fmt.Println(err.Error())
	}
	return string(bs)
}

func Get(gitVersion, gitCommit, buildDate string) Version {
	return Version{
		GitVersion: gitVersion,
		GitCommit:  gitCommit,
		BuildDate:  buildDate,
		GoVersion:  runtime.Version(),
		Compiler:   runtime.Compiler,
		Platform:   fmt.Sprintf("%s/%s", runtime.GOOS, runtime.GOARCH),
	}
}

func GetVersion() string {
	v := Get(gitVersion, gitCommit, buildDate)
	return v.String()
}
