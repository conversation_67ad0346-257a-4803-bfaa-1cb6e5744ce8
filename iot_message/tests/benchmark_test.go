package tests

import (
	"context"
	"sync"
	"testing"
	"time"

	"git.cdtwzn.com/iot/iot_message/iot_message"
)

// BenchmarkPing 基准测试Ping接口
func BenchmarkPing(b *testing.B) {
	client := NewTestClient()
	req := &iot_message.Request{
		Ping: "benchmark",
	}

	b.<PERSON>()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			ctx, cancel := CreateTestContext()
			_, err := client.Ping(ctx, req)
			cancel()
			if err != nil {
				b.<PERSON><PERSON>("Ping failed: %v", err)
			}
		}
	})
}

// BenchmarkSendMessage 基准测试发送消息接口
func BenchmarkSendMessage(b *testing.B) {
	client := NewTestClient()

	b.ResetTimer()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			ctx, cancel := CreateTestContext()

			message := CreateTestMessage(
				"Benchmark Message",
				CreateJSONContent("Benchmark message content", map[string]interface{}{
					"action":   "benchmark",
					"testType": "performance",
					"benchmark": map[string]interface{}{
						"operation": "send_message",
						"timestamp": time.Now().Unix(),
					},
				}),
				iot_message.MessageType_MESSAGE_TYPE_NORMAL,
				"system",
				10001,
			)

			receivers := []*iot_message.Receiver{
				CreateTestReceiver("user", 20001),
			}

			req := &iot_message.SendMessageRequest{
				Message:   message,
				Receivers: receivers,
			}

			_, err := client.SendMessage(ctx, req)
			cancel()
			if err != nil {
				b.Errorf("SendMessage failed: %v", err)
			}
		}
	})
}

// BenchmarkSendMessageBatch 基准测试批量发送消息
func BenchmarkSendMessageBatch(b *testing.B) {
	client := NewTestClient()

	// 创建多个接收者
	receivers := make([]*iot_message.Receiver, 10)
	for i := 0; i < 10; i++ {
		receivers[i] = CreateTestReceiver("user", int64(30001+i))
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			ctx, cancel := CreateTestContext()

			message := CreateTestMessage(
				"Batch Benchmark Message",
				CreateJSONContent("Batch benchmark message content", map[string]interface{}{
					"action":   "benchmarkBatch",
					"testType": "performance",
					"benchmark": map[string]interface{}{
						"operation":      "batch_send_message",
						"receiversCount": len(receivers),
						"timestamp":      time.Now().Unix(),
					},
				}),
				iot_message.MessageType_MESSAGE_TYPE_NORMAL,
				"system",
				10002,
			)

			req := &iot_message.SendMessageRequest{
				Message:   message,
				Receivers: receivers,
			}

			_, err := client.SendMessage(ctx, req)
			cancel()
			if err != nil {
				b.Errorf("SendMessage batch failed: %v", err)
			}
		}
	})
}

// BenchmarkGetMessagesByUID 基准测试获取用户消息列表
func BenchmarkGetMessagesByUID(b *testing.B) {
	client := NewTestClient()

	// 预先发送一些消息
	ctx := context.Background()
	for i := 0; i < 5; i++ {
		message := CreateTestMessage(
			"Benchmark Setup Message",
			CreateJSONContent("Setup message for benchmark", map[string]interface{}{
				"action":   "benchmarkSetup",
				"testType": "performance",
				"setup": map[string]interface{}{
					"messageIndex": i,
					"purpose":      "benchmark_preparation",
					"timestamp":    time.Now().Unix(),
				},
			}),
			iot_message.MessageType_MESSAGE_TYPE_NORMAL,
			"system",
			10003,
		)

		receivers := []*iot_message.Receiver{
			CreateTestReceiver("user", 40001),
		}

		req := &iot_message.SendMessageRequest{
			Message:   message,
			Receivers: receivers,
		}

		client.SendMessage(ctx, req)
	}

	getReq := &iot_message.GetMessagesRequest{
		Receiver: CreateTestReceiver("user", 40001),
		Page:     1,
		PageSize: 10,
		Status:   0,
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			ctx, cancel := CreateTestContext()
			_, err := client.GetMessagesByUID(ctx, getReq)
			cancel()
			if err != nil {
				b.Errorf("GetMessagesByUID failed: %v", err)
			}
		}
	})
}

// BenchmarkGetMessagesBySenderID 基准测试根据发送者获取消息列表
func BenchmarkGetMessagesBySenderID(b *testing.B) {
	client := NewTestClient()

	// 预先发送一些消息
	ctx := context.Background()
	for i := 0; i < 5; i++ {
		message := CreateTestMessage(
			"Benchmark Setup Message",
			CreateJSONContent("Setup message for benchmark", map[string]interface{}{
				"action":   "benchmarkSetupSender",
				"testType": "performance",
				"setup": map[string]interface{}{
					"messageIndex": i,
					"purpose":      "sender_benchmark_preparation",
					"timestamp":    time.Now().Unix(),
				},
			}),
			iot_message.MessageType_MESSAGE_TYPE_NORMAL,
			"system",
			50001,
		)

		receivers := []*iot_message.Receiver{
			CreateTestReceiver("user", 50002),
		}

		req := &iot_message.SendMessageRequest{
			Message:   message,
			Receivers: receivers,
		}

		client.SendMessage(ctx, req)
	}

	getReq := &iot_message.GetMessagesRequest{
		Receiver: CreateTestReceiver("system", 50001),
		Page:     1,
		PageSize: 10,
		Status:   0,
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			ctx, cancel := CreateTestContext()
			_, err := client.GetMessagesBySenderID(ctx, getReq)
			cancel()
			if err != nil {
				b.Errorf("GetMessagesBySenderID failed: %v", err)
			}
		}
	})
}

// BenchmarkMarkAllMessagesAsRead 基准测试标记消息为已读
func BenchmarkMarkAllMessagesAsRead(b *testing.B) {
	client := NewTestClient()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			ctx, cancel := CreateTestContext()

			receiver := CreateTestReceiver("user", 60001)
			_, err := client.MarkAllMessagesAsRead(ctx, receiver)
			cancel()
			if err != nil {
				b.Errorf("MarkAllMessagesAsRead failed: %v", err)
			}
		}
	})
}

// BenchmarkConcurrentSendMessage 基准测试并发发送消息
func BenchmarkConcurrentSendMessage(b *testing.B) {
	client := NewTestClient()
	concurrency := 10

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			var wg sync.WaitGroup
			wg.Add(concurrency)

			for i := 0; i < concurrency; i++ {
				go func(id int) {
					defer wg.Done()

					ctx, cancel := CreateTestContext()
					defer cancel()

					message := CreateTestMessage(
						"Concurrent Benchmark Message",
						CreateJSONContent("Concurrent benchmark content", map[string]interface{}{
							"action":   "concurrentBenchmark",
							"testType": "performance",
							"concurrent": map[string]interface{}{
								"workerId":  id,
								"timestamp": time.Now().Unix(),
								"operation": "concurrent_send",
							},
						}),
						iot_message.MessageType_MESSAGE_TYPE_NORMAL,
						"system",
						int64(70000+id),
					)

					receivers := []*iot_message.Receiver{
						CreateTestReceiver("user", int64(80000+id)),
					}

					req := &iot_message.SendMessageRequest{
						Message:   message,
						Receivers: receivers,
					}

					_, err := client.SendMessage(ctx, req)
					if err != nil {
						b.Errorf("Concurrent SendMessage failed: %v", err)
					}
				}(i)
			}
			wg.Wait()
		}
	})
}

// BenchmarkGetMessageDetail 基准测试获取消息详情
func BenchmarkGetMessageDetail(b *testing.B) {
	client := NewTestClient()

	// 预先发送一条消息获取ID
	ctx := context.Background()
	message := CreateTestMessage(
		"Benchmark Detail Message",
		CreateJSONContent("Message for detail benchmark", map[string]interface{}{
			"action":   "benchmarkDetail",
			"testType": "performance",
			"detail": map[string]interface{}{
				"purpose":   "detail_retrieval_benchmark",
				"timestamp": time.Now().Unix(),
			},
		}),
		iot_message.MessageType_MESSAGE_TYPE_NORMAL,
		"system",
		90001,
	)

	receivers := []*iot_message.Receiver{
		CreateTestReceiver("user", 90002),
	}

	sendReq := &iot_message.SendMessageRequest{
		Message:   message,
		Receivers: receivers,
	}

	client.SendMessage(ctx, sendReq)

	// 获取消息ID
	getReq := &iot_message.GetMessagesRequest{
		Receiver: CreateTestReceiver("user", 90002),
		Page:     1,
		PageSize: 1,
		Status:   0,
	}

	listResp, _ := client.GetMessagesByUID(ctx, getReq)
	if len(listResp.Messages) == 0 {
		b.Skip("No messages found for benchmark")
	}

	messageID := listResp.Messages[0].Id
	detailReq := &iot_message.MessageDetailRequest{
		MessageId: messageID,
		Receiver:  CreateTestReceiver("user", 90002),
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			ctx, cancel := CreateTestContext()
			_, err := client.GetMessageDetail(ctx, detailReq)
			cancel()
			if err != nil {
				b.Errorf("GetMessageDetail failed: %v", err)
			}
		}
	})
}

// BenchmarkMixedOperations 基准测试混合操作
func BenchmarkMixedOperations(b *testing.B) {
	client := NewTestClient()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			ctx, cancel := CreateTestContext()

			// 发送消息
			message := CreateTestMessage(
				"Mixed Benchmark Message",
				CreateJSONContent("Mixed benchmark content", map[string]interface{}{
					"action":   "mixedBenchmark",
					"testType": "performance",
					"mixed": map[string]interface{}{
						"operations": []string{"send", "get", "ping"},
						"timestamp":  time.Now().Unix(),
					},
				}),
				iot_message.MessageType_MESSAGE_TYPE_NORMAL,
				"system",
				100001,
			)

			receivers := []*iot_message.Receiver{
				CreateTestReceiver("user", 100002),
			}

			sendReq := &iot_message.SendMessageRequest{
				Message:   message,
				Receivers: receivers,
			}

			_, err := client.SendMessage(ctx, sendReq)
			if err != nil {
				cancel()
				b.Errorf("Mixed operations SendMessage failed: %v", err)
				continue
			}

			// 获取消息列表
			getReq := &iot_message.GetMessagesRequest{
				Receiver: CreateTestReceiver("user", 100002),
				Page:     1,
				PageSize: 5,
				Status:   0,
			}

			_, err = client.GetMessagesByUID(ctx, getReq)
			if err != nil {
				cancel()
				b.Errorf("Mixed operations GetMessagesByUID failed: %v", err)
				continue
			}

			// Ping测试
			pingReq := &iot_message.Request{
				Ping: "mixed",
			}

			_, err = client.Ping(ctx, pingReq)
			cancel()
			if err != nil {
				b.Errorf("Mixed operations Ping failed: %v", err)
			}
		}
	})
}
