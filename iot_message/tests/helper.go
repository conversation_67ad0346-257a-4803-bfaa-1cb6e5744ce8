package tests

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"git.cdtwzn.com/iot/iot_message/iot_message"
	"git.cdtwzn.com/iot/iot_message/iotmessageclient"
	"github.com/zeromicro/go-zero/zrpc"
)

const (
	// RPC服务器地址
	ServerAddr = "127.0.0.1:6011"
	// 测试超时时间
	TestTimeout = 30 * time.Second
)

// 创建RPC客户端
func NewTestClient() iotmessageclient.IotMessage {
	client := zrpc.MustNewClient(zrpc.RpcClientConf{
		Endpoints: []string{ServerAddr},
		Timeout:   5000, // 5秒超时
	})
	return iotmessageclient.NewIotMessage(client)
}

// 创建测试消息
func CreateTestMessage(title, content string, messageType iot_message.MessageType, senderType string, senderID int64) *iot_message.Message {
	return &iot_message.Message{
		Title:      title,
		Content:    content,
		Mtype:      messageType,
		SenderType: senderType,
		SenderId:   senderID,
		CreatedAt:  time.Now().Format(time.RFC3339),
		ExpiredAt:  time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		Extra:      `{"test": true}`,
		Status:     0, // 未读
	}
}

// 创建JSON格式的测试消息内容
func CreateJSONContent(message string, data map[string]interface{}) string {
	content := map[string]interface{}{
		"message": message,
	}

	// 合并额外数据
	for k, v := range data {
		content[k] = v
	}

	jsonBytes, err := json.Marshal(content)
	if err != nil {
		// 如果序列化失败，返回一个基本的JSON格式
		return fmt.Sprintf(`{"message": "%s", "error": "failed to marshal data"}`, message)
	}

	return string(jsonBytes)
}

// 创建测试接收者
func CreateTestReceiver(receiverType string, receiverID int64) *iot_message.Receiver {
	return &iot_message.Receiver{
		Type: receiverType,
		Id:   receiverID,
	}
}

// 创建测试发送者
func CreateTestSender(senderType string, senderID int64) *iot_message.Sender {
	return &iot_message.Sender{
		Type: senderType,
		Id:   senderID,
	}
}

// 等待函数，用于给服务器处理时间
func WaitForProcessing() {
	time.Sleep(100 * time.Millisecond)
}

// 日志辅助函数
func LogTestResult(testName string, success bool, message string) {
	status := "✓"
	if !success {
		status = "✗"
	}
	log.Printf("[%s] %s: %s", status, testName, message)
}

// 创建上下文
func CreateTestContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), TestTimeout)
}

// 验证消息响应
func ValidateMessage(msg *iot_message.Message) error {
	if msg == nil {
		return fmt.Errorf("message is nil")
	}
	if msg.Id <= 0 {
		return fmt.Errorf("invalid message ID: %d", msg.Id)
	}
	if msg.Title == "" {
		return fmt.Errorf("message title is empty")
	}
	if msg.Content == "" {
		return fmt.Errorf("message content is empty")
	}

	// 验证Content是否为有效的JSON格式
	if err := ValidateJSONContent(msg.Content); err != nil {
		return fmt.Errorf("message content is not valid JSON: %v", err)
	}

	return nil
}

// 验证JSON内容格式
func ValidateJSONContent(content string) error {
	var data interface{}
	if err := json.Unmarshal([]byte(content), &data); err != nil {
		return fmt.Errorf("invalid JSON format: %v", err)
	}
	return nil
}

// 验证消息列表响应
func ValidateMessagesResponse(resp *iot_message.GetMessagesResponse) error {
	if resp == nil {
		return fmt.Errorf("response is nil")
	}
	if resp.Total < 0 {
		return fmt.Errorf("invalid total count: %d", resp.Total)
	}
	for i, msg := range resp.Messages {
		if err := ValidateMessage(msg); err != nil {
			return fmt.Errorf("invalid message at index %d: %v", i, err)
		}
	}
	return nil
}
