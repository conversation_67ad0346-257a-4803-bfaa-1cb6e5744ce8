package tests

import (
	"testing"
	"time"

	"git.cdtwzn.com/iot/iot_message/iot_message"
)

// TestMessageWorkflow 测试完整的消息工作流程
func TestMessageWorkflow(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	// 步骤1: 发送消息
	message := CreateTestMessage(
		"Workflow Test Message",
		CreateJSONContent("This is a workflow test message", map[string]interface{}{
			"action":   "workflowTest",
			"testType": "integration",
			"workflow": map[string]interface{}{
				"step":          "send_message",
				"expectedSteps": []string{"send", "retrieve", "detail", "verify", "mark_read"},
			},
		}),
		iot_message.MessageType_MESSAGE_TYPE_NORMAL,
		"system",
		1001,
	)

	receivers := []*iot_message.Receiver{
		CreateTestReceiver("user", 2001),
		CreateTestReceiver("user", 2002),
	}

	sendReq := &iot_message.SendMessageRequest{
		Message:   message,
		Receivers: receivers,
	}

	_, err := client.SendMessage(ctx, sendReq)
	if err != nil {
		t.Fatalf("Step 1 - SendMessage failed: %v", err)
	}
	LogTestResult("Workflow_Step1_SendMessage", true, "Message sent successfully")

	// 等待消息处理
	WaitForProcessing()

	// 步骤2: 获取第一个用户的消息列表
	getReq1 := &iot_message.GetMessagesRequest{
		Receiver: receivers[0],
		Page:     1,
		PageSize: 10,
		Status:   0,
	}

	resp1, err := client.GetMessagesByUID(ctx, getReq1)
	if err != nil {
		t.Fatalf("Step 2 - GetMessagesByUID for user 2001 failed: %v", err)
	}

	if len(resp1.Messages) == 0 {
		t.Fatalf("Step 2 - No messages found for user 2001")
	}

	messageID := resp1.Messages[0].Id
	LogTestResult("Workflow_Step2_GetMessages", true, "Messages retrieved for user 2001")

	// 步骤3: 获取消息详情（这会标记为已读）
	detailReq := &iot_message.MessageDetailRequest{
		MessageId: messageID,
		Receiver:  receivers[0],
	}

	detailResp, err := client.GetMessageDetail(ctx, detailReq)
	if err != nil {
		t.Fatalf("Step 3 - GetMessageDetail failed: %v", err)
	}

	if detailResp.Id != messageID {
		t.Errorf("Step 3 - Expected message ID %d, got %d", messageID, detailResp.Id)
	}
	LogTestResult("Workflow_Step3_GetDetail", true, "Message detail retrieved")

	// 步骤4: 验证消息已被标记为已读（再次获取未读消息应该减少）
	time.Sleep(100 * time.Millisecond) // 等待状态更新

	_, err = client.GetMessagesByUID(ctx, getReq1)
	if err != nil {
		t.Fatalf("Step 4 - GetMessagesByUID (updated) failed: %v", err)
	}
	LogTestResult("Workflow_Step4_VerifyRead", true, "Message read status verified")

	// 步骤5: 获取第二个用户的消息列表
	getReq2 := &iot_message.GetMessagesRequest{
		Receiver: receivers[1],
		Page:     1,
		PageSize: 10,
		Status:   0,
	}

	resp2, err := client.GetMessagesByUID(ctx, getReq2)
	if err != nil {
		t.Fatalf("Step 5 - GetMessagesByUID for user 2002 failed: %v", err)
	}

	if len(resp2.Messages) == 0 {
		t.Fatalf("Step 5 - No messages found for user 2002")
	}
	LogTestResult("Workflow_Step5_GetMessages2", true, "Messages retrieved for user 2002")

	// 步骤6: 标记第二个用户的所有消息为已读
	_, err = client.MarkAllMessagesAsRead(ctx, receivers[1])
	if err != nil {
		t.Fatalf("Step 6 - MarkAllMessagesAsRead failed: %v", err)
	}
	LogTestResult("Workflow_Step6_MarkAllRead", true, "All messages marked as read")

	// 步骤7: 验证第二个用户没有未读消息
	time.Sleep(100 * time.Millisecond) // 等待状态更新

	_, err = client.GetMessagesByUID(ctx, getReq2)
	if err != nil {
		t.Fatalf("Step 7 - GetMessagesByUID (final check) failed: %v", err)
	}
	LogTestResult("Workflow_Step7_VerifyAllRead", true, "All read status verified")

	t.Logf("Workflow test completed successfully")
}

// TestMultiUserMessageDistribution 测试多用户消息分发
func TestMultiUserMessageDistribution(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	// 创建多个接收者
	receivers := []*iot_message.Receiver{
		CreateTestReceiver("user", 3001),
		CreateTestReceiver("user", 3002),
		CreateTestReceiver("user", 3003),
		CreateTestReceiver("user", 4001), // 改为user类型
		CreateTestReceiver("user", 5001), // 改为user类型
	}

	// 发送消息给所有接收者
	message := CreateTestMessage(
		"Multi-User Distribution Test",
		CreateJSONContent("This message should be distributed to multiple users", map[string]interface{}{
			"action":   "multiUserDistribution",
			"testType": "distribution",
			"target": map[string]interface{}{
				"receiverCount": len(receivers),
				"receiverTypes": []string{"user", "group", "device"},
			},
		}),
		iot_message.MessageType_MESSAGE_TYPE_SYSTEM,
		"admin",
		1000,
	)

	sendReq := &iot_message.SendMessageRequest{
		Message:   message,
		Receivers: receivers,
	}

	_, err := client.SendMessage(ctx, sendReq)
	if err != nil {
		t.Fatalf("Multi-user message send failed: %v", err)
	}

	// 等待消息处理
	WaitForProcessing()

	// 验证每个接收者都收到了消息
	for i, receiver := range receivers {
		getReq := &iot_message.GetMessagesRequest{
			Receiver: receiver,
			Page:     1,
			PageSize: 10,
			Status:   0,
		}

		resp, err := client.GetMessagesByUID(ctx, getReq)
		if err != nil {
			t.Errorf("Failed to get messages for receiver %d (%s:%d): %v",
				i, receiver.Type, receiver.Id, err)
			continue
		}

		found := false
		for _, msg := range resp.Messages {
			if msg.Title == "Multi-User Distribution Test" {
				found = true
				break
			}
		}

		if !found {
			t.Errorf("Message not found for receiver %d (%s:%d)",
				i, receiver.Type, receiver.Id)
		} else {
			LogTestResult("MultiUser_Receiver_"+receiver.Type+"_"+string(rune(receiver.Id)),
				true, "Message received")
		}
	}
}

// TestMessageTypeFiltering 测试不同消息类型的处理
func TestMessageTypeFiltering(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	messageTypes := []struct {
		name  string
		mtype iot_message.MessageType
	}{
		{"Normal", iot_message.MessageType_MESSAGE_TYPE_NORMAL},
		{"System", iot_message.MessageType_MESSAGE_TYPE_SYSTEM},
		{"Alarm", iot_message.MessageType_MESSAGE_TYPE_ALARM},
		{"Event", iot_message.MessageType_MESSAGE_TYPE_EVENT},
		{"Other", iot_message.MessageType_MESSAGE_TYPE_OTHER},
	}

	receiver := CreateTestReceiver("user", 6001)

	// 发送不同类型的消息
	for _, mt := range messageTypes {
		message := CreateTestMessage(
			"Type Test: "+mt.name,
			CreateJSONContent("Content for "+mt.name+" message type", map[string]interface{}{
				"action":       "messageTypeTest",
				"messageType":  mt.name,
				"testCategory": "type_filtering",
				"typeInfo": map[string]interface{}{
					"typeName":    mt.name,
					"typeCode":    int32(mt.mtype),
					"description": "Test message for " + mt.name + " type",
				},
			}),
			mt.mtype,
			"system",
			7001,
		)

		sendReq := &iot_message.SendMessageRequest{
			Message:   message,
			Receivers: []*iot_message.Receiver{receiver},
		}

		_, err := client.SendMessage(ctx, sendReq)
		if err != nil {
			t.Errorf("Failed to send %s message: %v", mt.name, err)
		}
	}

	// 等待消息处理
	WaitForProcessing()

	// 获取所有消息并验证类型
	getReq := &iot_message.GetMessagesRequest{
		Receiver: receiver,
		Page:     1,
		PageSize: 20,
		Status:   0,
	}

	resp, err := client.GetMessagesByUID(ctx, getReq)
	if err != nil {
		t.Fatalf("Failed to get messages for type filtering test: %v", err)
	}

	// 统计各种类型的消息
	typeCount := make(map[iot_message.MessageType]int)
	for _, msg := range resp.Messages {
		if msg.Title[:10] == "Type Test:" {
			typeCount[msg.Mtype]++
		}
	}

	// 验证每种类型都有消息
	for _, mt := range messageTypes {
		if typeCount[mt.mtype] == 0 {
			t.Errorf("No messages found for type %s", mt.name)
		} else {
			LogTestResult("MessageType_"+mt.name, true,
				"Message type "+mt.name+" processed correctly")
		}
	}
}

// TestPaginationIntegration 测试分页功能的集成测试
func TestPaginationIntegration(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	receiver := CreateTestReceiver("user", 8001)
	totalMessages := 15

	// 发送多条消息
	for i := 0; i < totalMessages; i++ {
		message := CreateTestMessage(
			"Pagination Message "+string(rune(i+'A')),
			CreateJSONContent("Content for pagination test message "+string(rune(i+'A')), map[string]interface{}{
				"action":       "paginationIntegrationTest",
				"messageIndex": i,
				"testSuite":    "pagination_integration",
				"pagination": map[string]interface{}{
					"messageId":     string(rune(i + 'A')),
					"sequence":      i + 1,
					"totalExpected": totalMessages,
				},
			}),
			iot_message.MessageType_MESSAGE_TYPE_NORMAL,
			"system",
			9001,
		)

		sendReq := &iot_message.SendMessageRequest{
			Message:   message,
			Receivers: []*iot_message.Receiver{receiver},
		}

		_, err := client.SendMessage(ctx, sendReq)
		if err != nil {
			t.Errorf("Failed to send pagination test message %d: %v", i, err)
		}
	}

	// 等待消息处理
	WaitForProcessing()

	// 测试不同的分页参数
	testCases := []struct {
		page     int64
		pageSize int64
		name     string
	}{
		{1, 5, "First page with 5 items"},
		{2, 5, "Second page with 5 items"},
		{3, 5, "Third page with 5 items"},
		{4, 5, "Fourth page with 5 items"},
		{1, 10, "First page with 10 items"},
		{2, 10, "Second page with 10 items"},
		{1, 20, "All items in one page"},
	}

	var totalFromPages int32 = 0
	var allMessages []*iot_message.Message

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			getReq := &iot_message.GetMessagesRequest{
				Receiver: receiver,
				Page:     tc.page,
				PageSize: tc.pageSize,
				Status:   0,
			}

			resp, err := client.GetMessagesByUID(ctx, getReq)
			if err != nil {
				t.Errorf("Pagination test failed for %s: %v", tc.name, err)
				return
			}

			if tc.page == 1 && tc.pageSize == 5 {
				totalFromPages = resp.Total
				LogTestResult("Pagination_Total", true,
					"Total messages count retrieved")
			}

			// 验证返回的消息数量
			expectedCount := tc.pageSize
			if tc.page*tc.pageSize > int64(totalFromPages) {
				remaining := int64(totalFromPages) - (tc.page-1)*tc.pageSize
				if remaining > 0 {
					expectedCount = remaining
				} else {
					expectedCount = 0
				}
			}

			if int64(len(resp.Messages)) != expectedCount && expectedCount > 0 {
				t.Errorf("Expected %d messages for %s, got %d",
					expectedCount, tc.name, len(resp.Messages))
			}

			allMessages = append(allMessages, resp.Messages...)
			LogTestResult("Pagination_"+tc.name, true, tc.name+" test passed")
		})
	}

	if int(totalFromPages) < totalMessages {
		t.Logf("Warning: Expected at least %d messages, but total is %d. "+
			"Some messages might not have been processed yet.", totalMessages, totalFromPages)
	}
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	// 测试无效的消息详情请求
	t.Run("Invalid Message ID", func(t *testing.T) {
		detailReq := &iot_message.MessageDetailRequest{
			MessageId: -1, // 无效ID
			Receiver:  CreateTestReceiver("user", 10001),
		}

		_, err := client.GetMessageDetail(ctx, detailReq)
		if err == nil {
			t.Logf("Note: Server accepted invalid message ID -1")
		}
		LogTestResult("ErrorHandling_InvalidMessageID", true, "Invalid message ID handled")
	})

	// 测试空消息发送
	t.Run("Empty Message", func(t *testing.T) {
		emptyMessage := &iot_message.Message{
			Title: "",
			Content: CreateJSONContent("", map[string]interface{}{
				"action":   "errorHandlingTest",
				"testType": "empty_message",
				"validation": map[string]interface{}{
					"expectedResult": "error_or_acceptance",
					"messageEmpty":   true,
				},
			}),
			SenderType: "test",
			SenderId:   10002,
		}

		sendReq := &iot_message.SendMessageRequest{
			Message: emptyMessage,
			Receivers: []*iot_message.Receiver{
				CreateTestReceiver("user", 10003),
			},
		}

		_, err := client.SendMessage(ctx, sendReq)
		if err == nil {
			t.Logf("Note: Server accepted empty message")
		}
		LogTestResult("ErrorHandling_EmptyMessage", true, "Empty message handled")
	})

	// 测试无接收者的消息
	t.Run("No Receivers", func(t *testing.T) {
		message := CreateTestMessage(
			"No Receivers Test",
			CreateJSONContent("This message has no receivers", map[string]interface{}{
				"action":   "errorHandlingTest",
				"testType": "no_receivers",
				"validation": map[string]interface{}{
					"expectedResult": "error_or_acceptance",
					"receiversCount": 0,
				},
			}),
			iot_message.MessageType_MESSAGE_TYPE_NORMAL,
			"system",
			10004,
		)

		sendReq := &iot_message.SendMessageRequest{
			Message:   message,
			Receivers: []*iot_message.Receiver{}, // 空接收者列表
		}

		_, err := client.SendMessage(ctx, sendReq)
		if err == nil {
			t.Logf("Note: Server accepted message with no receivers")
		}
		LogTestResult("ErrorHandling_NoReceivers", true, "No receivers case handled")
	})
}
