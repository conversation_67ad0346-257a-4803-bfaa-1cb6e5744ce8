# Makefile for IoT Message RPC Tests

.PHONY: test benchmark integration clean help

# Default target
all: test

# Run all tests
test:
	@echo "Running all tests..."
	@./run_tests.sh

# Run unit tests only
unit:
	@echo "Running unit tests..."
	@go test -v -run="^Test" -timeout=60s

# Run benchmark tests only
benchmark:
	@echo "Running benchmark tests..."
	@go test -v -bench="^Benchmark" -benchmem -timeout=300s

# Run integration tests only
integration:
	@echo "Running integration tests..."
	@go test -v -run="^TestMessageWorkflow|^TestMultiUser|^TestMessageType|^TestPagination|^TestError" -timeout=120s

# Run concurrent tests
concurrent:
	@echo "Running concurrent tests..."
	@go test -v -run="^TestConcurrentRequests" -timeout=60s

# Run performance stress tests
stress:
	@echo "Running stress tests..."
	@go test -bench="^BenchmarkConcurrent" -benchtime=30s -timeout=600s

# Quick ping test to check server connectivity
ping:
	@echo "Testing server connectivity..."
	@go test -v -run="^TestPing" -timeout=10s

# Generate test coverage report
coverage:
	@echo "Generating test coverage report..."
	@go test -coverprofile=coverage.out -timeout=120s
	@go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Run tests with race detection
race:
	@echo "Running tests with race detection..."
	@go test -race -v -timeout=120s

# Run specific test
# Usage: make run-test TEST=TestPing
run-test:
	@echo "Running test: $(TEST)"
	@go test -v -run="$(TEST)" -timeout=60s

# Run specific benchmark
# Usage: make run-benchmark BENCH=BenchmarkPing
run-benchmark:
	@echo "Running benchmark: $(BENCH)"
	@go test -bench="$(BENCH)" -benchmem -timeout=120s

# Check server status
check-server:
	@echo "Checking server status..."
	@nc -z 127.0.0.1 6011 && echo "✅ Server is running" || echo "❌ Server is not running"

# Clean test artifacts
clean:
	@echo "Cleaning test artifacts..."
	@rm -f coverage.out coverage.html
	@go clean -testcache

# Format test files
fmt:
	@echo "Formatting test files..."
	@gofmt -w .

# Lint test files
lint:
	@echo "Linting test files..."
	@golangci-lint run

# Install test dependencies
deps:
	@echo "Installing test dependencies..."
	@go mod tidy
	@go mod download

# Help
help:
	@echo "Available targets:"
	@echo "  all         - Run all tests (default)"
	@echo "  test        - Run all tests using script"
	@echo "  unit        - Run unit tests only"
	@echo "  benchmark   - Run benchmark tests only"
	@echo "  integration - Run integration tests only"
	@echo "  concurrent  - Run concurrent tests only"
	@echo "  stress      - Run stress tests"
	@echo "  ping        - Quick ping test"
	@echo "  coverage    - Generate test coverage report"
	@echo "  race        - Run tests with race detection"
	@echo "  run-test    - Run specific test (TEST=TestName)"
	@echo "  run-benchmark - Run specific benchmark (BENCH=BenchmarkName)"
	@echo "  check-server - Check if server is running"
	@echo "  clean       - Clean test artifacts"
	@echo "  fmt         - Format test files"
	@echo "  lint        - Lint test files"
	@echo "  deps        - Install test dependencies"
	@echo "  help        - Show this help"
