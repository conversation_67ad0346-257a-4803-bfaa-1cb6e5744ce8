# IoT Message 测试用例 JSON Content 修改说明

## 概述

本次修改将 IoT Message 测试用例中的 `Content` 字段从普通字符串格式改为 JSON 格式，以便支持更灵活的消息内容扩展。

## 修改内容

### 1. Helper 函数增强

在 `tests/helper.go` 中新增了以下功能：

- **CreateJSONContent()** - 创建 JSON 格式的消息内容
- **ValidateJSONContent()** - 验证内容是否为有效 JSON 格式
- **ValidateMessage()** - 增强了消息验证，包括 JSON 格式检查

### 2. JSON 内容格式标准

所有测试用例的消息内容现在都采用以下 JSON 格式：

```json
{
  "message": "具体的消息文本",
  "action": "操作类型标识",
  "testType": "测试类型",
  "data": {
    // 具体的业务数据
  },
  "metadata": {
    // 元数据信息
  }
}
```

### 3. 修改的测试文件

- `iot_message_test.go` - 基础功能测试
- `integration_test.go` - 集成测试
- `benchmark_test.go` - 性能测试

### 4. 新增测试用例

新增了 `TestJSONContentValidation` 测试用例，专门测试复杂 JSON 内容的处理：

- 支持嵌套 JSON 对象
- 支持数组数据
- 支持多种数据类型（字符串、数字、布尔值等）

## 示例

### 简单 JSON 内容
```go
content := CreateJSONContent("Test message", map[string]interface{}{
    "action": "test",
    "priority": "normal",
})
```

生成的 JSON：
```json
{
  "message": "Test message",
  "action": "test",
  "priority": "normal"
}
```

### 复杂 JSON 内容
```go
content := CreateJSONContent("Complex message", map[string]interface{}{
    "user": map[string]interface{}{
        "id": 12345,
        "profile": map[string]interface{}{
            "email": "<EMAIL>",
            "interests": []string{"tech", "testing"},
        },
    },
    "device": map[string]interface{}{
        "type": "sensor",
        "readings": []map[string]interface{}{
            {"temperature": 22.5, "humidity": 45.2},
        },
    },
})
```

## 业务优势

1. **格式标准化** - 所有消息内容都采用统一的 JSON 格式
2. **可扩展性** - 业务可以根据需求自由组合消息内容结构
3. **类型安全** - JSON 格式提供了数据类型的明确定义
4. **易于解析** - 前端和其他服务可以轻松解析 JSON 格式的消息内容
5. **向后兼容** - 新的 JSON 格式不影响现有的消息处理逻辑

## 验证方法

运行以下命令验证所有测试：

```bash
cd tests
go test -v
```

运行 JSON 格式验证测试：

```bash
go test -v -run TestJSONContentValidation
```

## 注意事项

- 所有的消息 Content 字段必须是有效的 JSON 格式
- 建议在实际业务中也采用相同的 JSON 格式标准
- 如果 JSON 序列化失败，系统会返回包含错误信息的默认 JSON 格式
