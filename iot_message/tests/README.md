# IoT Message RPC Interface Tests

这个目录包含了IoT消息服务的RPC接口测试用例，涵盖了功能测试、性能基准测试和集成测试。

## 测试概览

### 服务器配置
- **RPC服务器地址**: `127.0.0.1:6011`
- **协议**: gRPC
- **超时时间**: 30秒

### 测试文件结构

```
tests/
├── helper.go              # 测试辅助函数和工具
├── iot_message_test.go     # 基本功能测试
├── benchmark_test.go       # 性能基准测试
├── integration_test.go     # 集成测试
├── run_tests.sh           # 测试运行脚本
└── README.md              # 本文档
```

## 测试用例覆盖

### 1. 基本功能测试 (iot_message_test.go)

#### Ping接口测试
- `TestPing`: 测试服务器连通性

#### 消息发送测试
- `TestSendMessage`: 基本消息发送功能
- `TestSendMessageWithDifferentTypes`: 测试不同消息类型发送
  - 普通消息 (MESSAGE_TYPE_NORMAL)
  - 系统消息 (MESSAGE_TYPE_SYSTEM)
  - 告警消息 (MESSAGE_TYPE_ALARM)
  - 事件消息 (MESSAGE_TYPE_EVENT)
  - 其他消息 (MESSAGE_TYPE_OTHER)

#### 消息获取测试
- `TestGetMessagesByUID`: 根据用户ID获取消息列表
- `TestGetMessagesBySenderID`: 根据发送者ID获取消息列表
- `TestGetMessageDetail`: 获取消息详情并自动标记为已读

#### 消息状态管理测试
- `TestMarkAllMessagesAsRead`: 批量标记消息为已读状态

#### 分页功能测试
- `TestPagination`: 测试消息列表的分页功能

#### 并发测试
- `TestConcurrentRequests`: 测试并发请求处理能力

### 2. 性能基准测试 (benchmark_test.go)

#### 单接口性能测试
- `BenchmarkPing`: Ping接口性能
- `BenchmarkSendMessage`: 单消息发送性能
- `BenchmarkSendMessageBatch`: 批量消息发送性能
- `BenchmarkGetMessagesByUID`: 获取用户消息列表性能
- `BenchmarkGetMessagesBySenderID`: 获取发送者消息列表性能
- `BenchmarkGetMessageDetail`: 获取消息详情性能
- `BenchmarkMarkAllMessagesAsRead`: 标记已读性能

#### 高并发性能测试
- `BenchmarkConcurrentSendMessage`: 并发发送消息性能
- `BenchmarkMixedOperations`: 混合操作性能测试

### 3. 集成测试 (integration_test.go)

#### 完整工作流测试
- `TestMessageWorkflow`: 测试完整的消息生命周期
  1. 发送消息
  2. 获取消息列表
  3. 获取消息详情（自动标记已读）
  4. 验证读取状态
  5. 批量标记已读

#### 多用户测试
- `TestMultiUserMessageDistribution`: 测试消息多用户分发功能

#### 消息类型测试
- `TestMessageTypeFiltering`: 测试不同消息类型的处理

#### 分页集成测试
- `TestPaginationIntegration`: 测试分页功能的完整流程

#### 错误处理测试
- `TestErrorHandling`: 测试各种错误情况的处理
  - 无效消息ID
  - 空消息内容
  - 无接收者消息

## 运行测试

### 前置条件

1. 确保RPC服务器运行在 `127.0.0.1:6011`
2. 安装Go 1.25.0或更高版本
3. 确保所有依赖包已安装

### 运行方式

#### 1. 使用脚本运行全部测试

```bash
cd tests/
./run_tests.sh
```

#### 2. 运行特定类型的测试

```bash
# 运行基本功能测试
go test -v -run="^Test" -timeout=60s

# 运行性能基准测试
go test -v -bench="^Benchmark" -benchmem -timeout=300s

# 运行集成测试
go test -v -run="^TestMessageWorkflow|^TestMultiUser|^TestMessageType|^TestPagination|^TestError" -timeout=120s

# 运行并发测试
go test -v -run="^TestConcurrentRequests" -timeout=60s
```

#### 3. 运行特定测试用例

```bash
# 运行Ping测试
go test -v -run="TestPing"

# 运行消息发送测试
go test -v -run="TestSendMessage"

# 运行基准测试
go test -bench="BenchmarkPing" -benchmem
```

### 性能基准测试参数

```bash
# 运行更长时间的基准测试
go test -bench="." -benchtime=30s -benchmem

# 运行特定次数的基准测试
go test -bench="." -count=5 -benchmem

# 运行并发基准测试
go test -bench="BenchmarkConcurrent" -benchtime=10s
```

## 测试数据说明

### 测试用户ID范围
- 基本功能测试: 1000-9999
- 性能测试: 10000-99999
- 集成测试: 1000-9999 (与功能测试共享)

### 测试消息格式
- 标题: 描述性标题
- 内容: 测试用途说明
- 类型: 根据测试需求选择
- 发送者: 通常为"system"
- 附加信息: JSON格式的测试标识

## 测试结果分析

### 功能测试结果
- ✅ 通过: 功能正常
- ❌ 失败: 需要检查服务器实现
- ⚠️ 警告: 部分功能可能需要优化

### 性能测试指标
- **ns/op**: 每次操作的纳秒数
- **B/op**: 每次操作分配的字节数
- **allocs/op**: 每次操作的内存分配次数

### 建议的性能基准
- Ping操作: < 1ms
- 发送消息: < 10ms
- 获取消息列表: < 50ms
- 获取消息详情: < 20ms

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器是否运行在 127.0.0.1:6011
   - 确认防火墙设置
   - 验证网络连接

2. **测试超时**
   - 增加超时时间: `-timeout=120s`
   - 检查服务器性能
   - 减少并发数量

3. **内存不足**
   - 减少测试数据量
   - 调整Go内存限制: `GOGC=100`

4. **数据库问题**
   - 检查数据库连接
   - 确认数据库权限
   - 验证数据一致性

### 调试模式

```bash
# 启用详细日志
go test -v -run="TestPing" -args -v

# 查看HTTP追踪
GODEBUG=http2debug=1 go test -v -run="TestPing"
```

## 扩展测试

### 添加新测试用例

1. 在相应的测试文件中添加新函数
2. 使用统一的命名约定: `Test*`, `Benchmark*`
3. 使用helper.go中的辅助函数
4. 添加适当的日志记录

### 自定义配置

可以通过环境变量自定义测试配置:

```bash
# 自定义服务器地址
export IOT_MESSAGE_SERVER="127.0.0.1:6011"

# 自定义超时时间
export IOT_MESSAGE_TIMEOUT="60s"
```

## 贡献指南

1. 保持测试用例的独立性
2. 使用有意义的测试数据
3. 添加适当的错误处理
4. 更新文档和注释
5. 运行所有测试确保无回归
