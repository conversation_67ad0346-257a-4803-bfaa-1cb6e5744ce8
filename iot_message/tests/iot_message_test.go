package tests

import (
	"testing"
	"time"

	"git.cdtwzn.com/iot/iot_message/iot_message"
)

// TestPing 测试Ping接口
func TestPing(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	req := &iot_message.Request{
		Ping: "hello",
	}

	resp, err := client.Ping(ctx, req)
	if err != nil {
		t.Fatalf("Ping failed: %v", err)
	}

	if resp.Pong == "" {
		t.<PERSON>rro<PERSON>("Expected non-empty pong response, got empty")
	}

	LogTestResult("Ping", true, "Ping test passed")
}

// TestSendMessage 测试发送消息接口
func TestSendMessage(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	// 创建测试消息
	message := CreateTestMessage(
		"Test Message Title",
		CreateJSONContent("This is a test message content", map[string]interface{}{
			"type":     "test",
			"priority": "normal",
			"source":   "unit_test",
		}),
		iot_message.MessageType_MESSAGE_TYPE_NORMAL,
		"system",
		1001,
	)

	// 创建接收者列表
	receivers := []*iot_message.Receiver{
		CreateTestReceiver("user", 2001),
		CreateTestReceiver("user", 2002),
	}

	req := &iot_message.SendMessageRequest{
		Message:   message,
		Receivers: receivers,
	}

	resp, err := client.SendMessage(ctx, req)
	if err != nil {
		t.Fatalf("SendMessage failed: %v", err)
	}

	if resp == nil {
		t.Errorf("Expected non-nil response")
	}

	LogTestResult("SendMessage", true, "SendMessage test passed")
}

// TestSendMessageWithDifferentTypes 测试发送不同类型的消息
func TestSendMessageWithDifferentTypes(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	messageTypes := []struct {
		name  string
		mtype iot_message.MessageType
	}{
		{"Normal Message", iot_message.MessageType_MESSAGE_TYPE_NORMAL},
		{"System Message", iot_message.MessageType_MESSAGE_TYPE_SYSTEM},
		{"Alarm Message", iot_message.MessageType_MESSAGE_TYPE_ALARM},
		{"Event Message", iot_message.MessageType_MESSAGE_TYPE_EVENT},
		{"Other Message", iot_message.MessageType_MESSAGE_TYPE_OTHER},
	}

	for _, mt := range messageTypes {
		t.Run(mt.name, func(t *testing.T) {
			message := CreateTestMessage(
				mt.name,
				CreateJSONContent("Content for "+mt.name, map[string]interface{}{
					"messageType": mt.name,
					"category":    "test",
					"details": map[string]interface{}{
						"timestamp": time.Now().Unix(),
						"source":    "test_suite",
					},
				}),
				mt.mtype,
				"system",
				1001,
			)

			receivers := []*iot_message.Receiver{
				CreateTestReceiver("user", 3001),
			}

			req := &iot_message.SendMessageRequest{
				Message:   message,
				Receivers: receivers,
			}

			resp, err := client.SendMessage(ctx, req)
			if err != nil {
				t.Errorf("SendMessage failed for %s: %v", mt.name, err)
				return
			}

			if resp == nil {
				t.Errorf("Expected non-nil response for %s", mt.name)
			}

			LogTestResult("SendMessage_"+mt.name, true, mt.name+" sent successfully")
		})
	}
}

// TestGetMessagesByUID 测试根据用户ID获取消息列表
func TestGetMessagesByUID(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	// 先发送一条消息
	message := CreateTestMessage(
		"Test Message for GetByUID",
		CreateJSONContent("Test content for GetByUID", map[string]interface{}{
			"action": "getUserMessages",
			"userId": 4001,
			"metadata": map[string]interface{}{
				"testType": "functional",
				"module":   "message_retrieval",
			},
		}),
		iot_message.MessageType_MESSAGE_TYPE_NORMAL,
		"system",
		1001,
	)

	receivers := []*iot_message.Receiver{
		CreateTestReceiver("user", 4001),
	}

	sendReq := &iot_message.SendMessageRequest{
		Message:   message,
		Receivers: receivers,
	}

	_, err := client.SendMessage(ctx, sendReq)
	if err != nil {
		t.Fatalf("Failed to send test message: %v", err)
	}

	// 等待消息处理
	WaitForProcessing()

	// 获取消息列表
	getReq := &iot_message.GetMessagesRequest{
		Receiver: CreateTestReceiver("user", 4001),
		Page:     1,
		PageSize: 10,
		Status:   0,
	}

	resp, err := client.GetMessagesByUID(ctx, getReq)
	if err != nil {
		t.Fatalf("GetMessagesByUID failed: %v", err)
	}

	if err := ValidateMessagesResponse(resp); err != nil {
		t.Errorf("Invalid messages response: %v", err)
	}

	LogTestResult("GetMessagesByUID", true, "GetMessagesByUID test passed")
}

// TestGetMessagesBySenderID 测试根据发送者ID获取消息列表
func TestGetMessagesBySenderID(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	// 先发送一条消息
	message := CreateTestMessage(
		"Test Message for GetBySenderID",
		CreateJSONContent("Test content for GetBySenderID", map[string]interface{}{
			"action":     "getSenderMessages",
			"senderId":   5001,
			"filterType": "by_sender",
			"metadata": map[string]interface{}{
				"testType": "functional",
				"module":   "sender_message_retrieval",
			},
		}),
		iot_message.MessageType_MESSAGE_TYPE_NORMAL,
		"system",
		5001,
	)

	receivers := []*iot_message.Receiver{
		CreateTestReceiver("user", 5002),
	}

	sendReq := &iot_message.SendMessageRequest{
		Message:   message,
		Receivers: receivers,
	}

	_, err := client.SendMessage(ctx, sendReq)
	if err != nil {
		t.Fatalf("Failed to send test message: %v", err)
	}

	// 等待消息处理
	WaitForProcessing()

	// 获取消息列表
	getReq := &iot_message.GetMessagesRequest{
		Receiver: CreateTestReceiver("system", 5001), // 这里使用发送者信息
		Page:     1,
		PageSize: 10,
		Status:   0,
	}

	resp, err := client.GetMessagesBySenderID(ctx, getReq)
	if err != nil {
		t.Fatalf("GetMessagesBySenderID failed: %v", err)
	}

	if err := ValidateMessagesResponse(resp); err != nil {
		t.Errorf("Invalid messages response: %v", err)
	}

	LogTestResult("GetMessagesBySenderID", true, "GetMessagesBySenderID test passed")
}

// TestGetMessageDetail 测试获取消息详情
func TestGetMessageDetail(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	// 先发送一条消息
	message := CreateTestMessage(
		"Test Message for Detail",
		CreateJSONContent("Test content for Detail", map[string]interface{}{
			"action":  "getMessageDetail",
			"purpose": "detail_testing",
			"details": map[string]interface{}{
				"requestType":      "detail",
				"expectedResponse": "full_message_info",
			},
		}),
		iot_message.MessageType_MESSAGE_TYPE_NORMAL,
		"system",
		6001,
	)

	receivers := []*iot_message.Receiver{
		CreateTestReceiver("user", 6002),
	}

	sendReq := &iot_message.SendMessageRequest{
		Message:   message,
		Receivers: receivers,
	}

	_, err := client.SendMessage(ctx, sendReq)
	if err != nil {
		t.Fatalf("Failed to send test message: %v", err)
	}

	// 等待消息处理
	WaitForProcessing()

	// 先获取消息列表以获得消息ID
	getReq := &iot_message.GetMessagesRequest{
		Receiver: CreateTestReceiver("user", 6002),
		Page:     1,
		PageSize: 1,
		Status:   0,
	}

	listResp, err := client.GetMessagesByUID(ctx, getReq)
	if err != nil {
		t.Fatalf("Failed to get messages: %v", err)
	}

	if len(listResp.Messages) == 0 {
		t.Fatalf("No messages found")
	}

	messageID := listResp.Messages[0].Id

	// 获取消息详情
	detailReq := &iot_message.MessageDetailRequest{
		MessageId: messageID,
		Receiver:  CreateTestReceiver("user", 6002),
	}

	detailResp, err := client.GetMessageDetail(ctx, detailReq)
	if err != nil {
		t.Fatalf("GetMessageDetail failed: %v", err)
	}

	if err := ValidateMessage(detailResp); err != nil {
		t.Errorf("Invalid message detail: %v", err)
	}

	if detailResp.Id != messageID {
		t.Errorf("Expected message ID %d, got %d", messageID, detailResp.Id)
	}

	LogTestResult("GetMessageDetail", true, "GetMessageDetail test passed")
}

// TestMarkAllMessagesAsRead 测试标记所有消息为已读
func TestMarkAllMessagesAsRead(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	// 先发送几条消息
	for i := 0; i < 3; i++ {
		message := CreateTestMessage(
			"Test Message for MarkAsRead "+string(rune(i+'A')),
			CreateJSONContent("Test content for MarkAsRead "+string(rune(i+'A')), map[string]interface{}{
				"action":       "markAllAsRead",
				"messageIndex": i,
				"batchId":      "mark_as_read_test",
				"data": map[string]interface{}{
					"sequence": i + 1,
					"total":    3,
				},
			}),
			iot_message.MessageType_MESSAGE_TYPE_NORMAL,
			"system",
			7001,
		)

		receivers := []*iot_message.Receiver{
			CreateTestReceiver("user", 7002),
		}

		sendReq := &iot_message.SendMessageRequest{
			Message:   message,
			Receivers: receivers,
		}

		_, err := client.SendMessage(ctx, sendReq)
		if err != nil {
			t.Fatalf("Failed to send test message %d: %v", i, err)
		}
	}

	// 等待消息处理
	WaitForProcessing()

	// 标记所有消息为已读
	markReq := CreateTestReceiver("user", 7002)

	resp, err := client.MarkAllMessagesAsRead(ctx, markReq)
	if err != nil {
		t.Fatalf("MarkAllMessagesAsRead failed: %v", err)
	}

	if resp == nil {
		t.Errorf("Expected non-nil response")
	}

	LogTestResult("MarkAllMessagesAsRead", true, "MarkAllMessagesAsRead test passed")
}

// TestPagination 测试分页功能
func TestPagination(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	// 发送多条消息
	for i := 0; i < 5; i++ {
		message := CreateTestMessage(
			"Pagination Test Message "+string(rune(i+'1')),
			CreateJSONContent("Pagination test content "+string(rune(i+'1')), map[string]interface{}{
				"action":    "paginationTest",
				"pageIndex": i + 1,
				"testSuite": "pagination",
				"data": map[string]interface{}{
					"messageNumber": i + 1,
					"totalMessages": 5,
					"category":      "pagination_test",
				},
			}),
			iot_message.MessageType_MESSAGE_TYPE_NORMAL,
			"system",
			8001,
		)

		receivers := []*iot_message.Receiver{
			CreateTestReceiver("user", 8002),
		}

		sendReq := &iot_message.SendMessageRequest{
			Message:   message,
			Receivers: receivers,
		}

		_, err := client.SendMessage(ctx, sendReq)
		if err != nil {
			t.Fatalf("Failed to send test message %d: %v", i, err)
		}
	}

	// 等待消息处理
	WaitForProcessing()

	// 测试分页
	testCases := []struct {
		page     int64
		pageSize int64
		name     string
	}{
		{1, 2, "Page 1 Size 2"},
		{2, 2, "Page 2 Size 2"},
		{1, 10, "Page 1 Size 10"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			getReq := &iot_message.GetMessagesRequest{
				Receiver: CreateTestReceiver("user", 8002),
				Page:     tc.page,
				PageSize: tc.pageSize,
				Status:   0,
			}

			resp, err := client.GetMessagesByUID(ctx, getReq)
			if err != nil {
				t.Errorf("Pagination test failed for %s: %v", tc.name, err)
				return
			}

			if err := ValidateMessagesResponse(resp); err != nil {
				t.Errorf("Invalid pagination response for %s: %v", tc.name, err)
			}

			LogTestResult("Pagination_"+tc.name, true, tc.name+" test passed")
		})
	}
}

// TestConcurrentRequests 测试并发请求
func TestConcurrentRequests(t *testing.T) {
	client := NewTestClient()

	// 并发发送消息
	concurrency := 5
	done := make(chan bool, concurrency)
	errors := make(chan error, concurrency)

	for i := 0; i < concurrency; i++ {
		go func(id int) {
			ctx, cancel := CreateTestContext()
			defer cancel()

			message := CreateTestMessage(
				"Concurrent Test Message "+string(rune(id+'A')),
				CreateJSONContent("Concurrent test content "+string(rune(id+'A')), map[string]interface{}{
					"action":      "concurrentTest",
					"goroutineId": id,
					"testType":    "concurrent",
					"data": map[string]interface{}{
						"workerId":  id,
						"timestamp": time.Now().Unix(),
					},
				}),
				iot_message.MessageType_MESSAGE_TYPE_NORMAL,
				"system",
				int64(9000+id),
			)

			receivers := []*iot_message.Receiver{
				CreateTestReceiver("user", int64(9100+id)),
			}

			req := &iot_message.SendMessageRequest{
				Message:   message,
				Receivers: receivers,
			}

			_, err := client.SendMessage(ctx, req)
			if err != nil {
				errors <- err
				return
			}
			done <- true
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < concurrency; i++ {
		select {
		case <-done:
			// 成功
		case err := <-errors:
			t.Errorf("Concurrent request failed: %v", err)
		case <-time.After(TestTimeout):
			t.Errorf("Concurrent request timeout")
		}
	}

	LogTestResult("ConcurrentRequests", true, "Concurrent requests test passed")
}

// TestJSONContentValidation 测试JSON格式内容验证
func TestJSONContentValidation(t *testing.T) {
	client := NewTestClient()
	ctx, cancel := CreateTestContext()
	defer cancel()

	// 测试复杂的JSON内容
	complexJSONContent := CreateJSONContent("Complex JSON message", map[string]interface{}{
		"action": "jsonValidationTest",
		"data": map[string]interface{}{
			"user": map[string]interface{}{
				"id":   12345,
				"name": "Test User",
				"profile": map[string]interface{}{
					"email":     "<EMAIL>",
					"age":       25,
					"interests": []string{"technology", "testing", "json"},
				},
			},
			"device": map[string]interface{}{
				"type":     "sensor",
				"location": "room_101",
				"readings": []map[string]interface{}{
					{"timestamp": time.Now().Unix(), "temperature": 22.5, "humidity": 45.2},
					{"timestamp": time.Now().Unix() - 3600, "temperature": 23.1, "humidity": 44.8},
				},
			},
		},
		"metadata": map[string]interface{}{
			"version":   "1.0",
			"timestamp": time.Now().Format(time.RFC3339),
			"flags":     []bool{true, false, true},
		},
	})

	message := CreateTestMessage(
		"JSON Validation Test",
		complexJSONContent,
		iot_message.MessageType_MESSAGE_TYPE_NORMAL,
		"system",
		11001,
	)

	receivers := []*iot_message.Receiver{
		CreateTestReceiver("user", 11002),
	}

	sendReq := &iot_message.SendMessageRequest{
		Message:   message,
		Receivers: receivers,
	}

	_, err := client.SendMessage(ctx, sendReq)
	if err != nil {
		t.Fatalf("Failed to send message with complex JSON content: %v", err)
	}

	// 等待消息处理
	WaitForProcessing()

	// 获取并验证消息
	getReq := &iot_message.GetMessagesRequest{
		Receiver: receivers[0],
		Page:     1,
		PageSize: 10,
		Status:   0,
	}

	resp, err := client.GetMessagesByUID(ctx, getReq)
	if err != nil {
		t.Fatalf("Failed to get messages: %v", err)
	}

	// 验证消息内容是有效的JSON
	found := false
	for _, msg := range resp.Messages {
		if msg.Title == "JSON Validation Test" {
			if err := ValidateMessage(msg); err != nil {
				t.Errorf("JSON validation failed: %v", err)
			} else {
				LogTestResult("JSONContentValidation", true, "Complex JSON content validated successfully")
				found = true
			}
			break
		}
	}

	if !found {
		t.Errorf("Test message not found in response")
	}
}
