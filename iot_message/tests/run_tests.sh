#!/bin/bash

# IoT Message RPC测试运行脚本
# RPC服务器地址: 127.0.0.1:6011

set -e

echo "======================================"
echo "IoT Message RPC Interface Test Suite"
echo "======================================"
echo "Server: 127.0.0.1:6011"
echo "Date: $(date)"
echo "======================================"

# 检查服务器是否可达
echo "🔍 Checking server connectivity..."
if ! nc -z 127.0.0.1 6011 2>/dev/null; then
    echo "❌ Error: RPC server is not running on 127.0.0.1:6011"
    echo "Please start the server before running tests."
    exit 1
fi
echo "✅ Server is reachable"

cd "$(dirname "$0")"

echo ""
echo "🧪 Running Unit Tests..."
echo "========================================"
go test -v -run="^Test" -timeout=60s

echo ""
echo "📊 Running Benchmark Tests..."
echo "========================================"
go test -v -bench="^Benchmark" -benchmem -timeout=300s

echo ""
echo "🔗 Running Integration Tests..."
echo "========================================"
go test -v -run="^TestMessageWorkflow|^TestMultiUser|^TestMessageType|^TestPagination|^TestError" -timeout=120s

echo ""
echo "📈 Running Performance Tests..."
echo "========================================"
echo "Testing concurrent load..."
go test -v -run="^TestConcurrentRequests" -timeout=60s

echo ""
echo "🎯 Running Stress Tests..."
echo "========================================"
echo "Running benchmarks with high load..."
go test -bench="^BenchmarkConcurrent" -benchtime=10s -timeout=300s

echo ""
echo "✅ All tests completed!"
echo "======================================"
