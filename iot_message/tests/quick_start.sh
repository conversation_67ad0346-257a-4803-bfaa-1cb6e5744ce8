#!/bin/bash

# IoT Message RPC测试快速开始指南

echo "=========================================="
echo "IoT Message RPC Interface Test Quick Start"
echo "=========================================="

echo ""
echo "📋 测试文件概览:"
echo "  ✅ helper.go          - 测试辅助函数"
echo "  ✅ iot_message_test.go - 基本功能测试"
echo "  ✅ benchmark_test.go   - 性能基准测试"
echo "  ✅ integration_test.go - 集成测试"
echo "  ✅ run_tests.sh        - 测试运行脚本"
echo "  ✅ Makefile           - Make构建文件"

echo ""
echo "🚀 快速运行测试:"
echo ""
echo "1. 检查服务器连接:"
echo "   make check-server"
echo ""
echo "2. 运行单个Ping测试:"
echo "   make ping"
echo ""
echo "3. 运行所有测试:"
echo "   make test"
echo ""
echo "4. 运行性能基准测试:"
echo "   make benchmark"
echo ""
echo "5. 运行集成测试:"
echo "   make integration"

echo ""
echo "📊 测试覆盖的RPC接口:"
echo "  🔹 Ping                    - 服务器连通性测试"
echo "  🔹 SendMessage             - 发送消息"
echo "  🔹 GetMessagesByUID        - 根据用户ID获取消息"
echo "  🔹 GetMessagesBySenderID   - 根据发送者ID获取消息"
echo "  🔹 GetMessageDetail        - 获取消息详情"
echo "  🔹 MarkAllMessagesAsRead   - 标记所有消息为已读"

echo ""
echo "🎯 性能基准测试覆盖:"
echo "  📈 单接口性能测试"
echo "  📈 批量操作性能测试"
echo "  📈 并发请求性能测试"
echo "  📈 混合操作性能测试"

echo ""
echo "🔧 服务器要求:"
echo "  📍 地址: 127.0.0.1:6011"
echo "  ⏱️  超时: 30秒"
echo "  🌐 协议: gRPC"

echo ""
echo "✨ 开始测试前请确保:"
echo "  1. RPC服务器正在运行"
echo "  2. 数据库连接正常"
echo "  3. 网络连接畅通"

echo ""
echo "🆘 如需帮助:"
echo "   make help"

echo ""
echo "=========================================="
