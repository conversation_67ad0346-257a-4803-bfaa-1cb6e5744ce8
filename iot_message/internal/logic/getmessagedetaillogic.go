package logic

import (
	"context"
	"database/sql"
	"time"

	"git.cdtwzn.com/iot/iot_message/internal/svc"
	"git.cdtwzn.com/iot/iot_message/iot_message"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMessageDetailLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetMessageDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMessageDetailLogic {
	return &GetMessageDetailLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取消息详情(内嵌实现已读状态)
func (l *GetMessageDetailLogic) GetMessageDetail(in *iot_message.MessageDetailRequest) (*iot_message.Message, error) {
	if in.MessageId <= 0 || in.Receiver == nil || in.Receiver.Id <= 0 {
		return nil, status.Error(codes.InvalidArgument, "invalid params")
	}
	d, err := l.svcCtx.DB.DB()
	if err != nil {
		return nil, status.Error(codes.Internal, "db error:"+err.Error())
	}
	ql := `
	select m.id,m.title,m.content,m.mtype,m.sender_id,m.created_at,m.expired_at,m.extra,
	       r.readed_at,r.status
	from messages m
	left join message_receivers r on m.id=r.message_id and r.receiver_id=? and r.receiver_type=?
	where m.id=? and (m.expired_at is null or m.expired_at>now()) and (r.deleted is null or r.deleted=0)
	`
	rs, err := d.Query(ql, in.Receiver.Id, in.Receiver.Type, in.MessageId)
	if err != nil {
		return nil, status.Error(codes.Internal, "db error:"+err.Error())
	}
	defer rs.Close()
	if !rs.Next() {
		return nil, status.Error(codes.NotFound, "message not found")
	}
	msg := &iot_message.Message{}
	var createdAt, expiredAt, readAt sql.NullTime
	if err := rs.Scan(&msg.Id, &msg.Title, &msg.Content, &msg.Mtype, &msg.SenderId,
		&createdAt, &expiredAt, &msg.Extra, &readAt, &msg.Status); err != nil {
		return nil, status.Error(codes.Internal, "db error:"+err.Error())
	}
	if createdAt.Valid {
		msg.CreatedAt = createdAt.Time.Format(time.DateTime)
	}
	if expiredAt.Valid {
		msg.ExpiredAt = expiredAt.Time.Format(time.DateTime)
	}
	if readAt.Valid {
		msg.ReadAt = readAt.Time.Format(time.DateTime)
	}
	return msg, nil
}
