package logic

import (
	"context"

	"git.cdtwzn.com/iot/iot_message/internal/svc"
	"git.cdtwzn.com/iot/iot_message/iot_message"
	"git.cdtwzn.com/iot/iot_message/pkg/usertypes"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteMessageLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteMessageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteMessageLogic {
	return &DeleteMessageLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除一条消息
func (l *DeleteMessageLogic) DeleteMessage(in *iot_message.MessageDeleteRequest) (*iot_message.Response, error) {
	if in.MessageId <= 0 || in.Receiver == nil || in.Receiver.Id <= 0 {
		return nil, status.Error(codes.InvalidArgument, "invalid params")
	}
	q := l.svcCtx.Query
	// 检查是否是有效的用户类型
	if !usertypes.CheckUserType(in.Receiver.Type) {
		return nil, status.Error(codes.InvalidArgument, "invalid user type")
	}

	_, err := q.MessageReceiver.WithContext(l.ctx).Where(
		q.MessageReceiver.MessageID.Eq(in.MessageId),
		q.MessageReceiver.ReceiverID.Eq(in.Receiver.Id),
		q.MessageReceiver.ReceiverType.Eq(in.Receiver.Type),
	).UpdateColumn(q.MessageReceiver.Status, 2)
	if err != nil {
		return nil, status.Error(codes.Internal, "db error:"+err.Error())
	}
	return &iot_message.Response{}, nil
}
