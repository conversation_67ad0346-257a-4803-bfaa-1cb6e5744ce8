package logic

import (
	"context"

	"git.cdtwzn.com/iot/iot_message/internal/svc"
	"git.cdtwzn.com/iot/iot_message/iot_message"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUnreadMessageCountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUnreadMessageCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUnreadMessageCountLogic {
	return &GetUnreadMessageCountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 根据UID获取用户未读消息数量
func (l *GetUnreadMessageCountLogic) GetUnreadMessageCount(in *iot_message.Receiver) (*iot_message.UnreadMessageCountResponse, error) {
	if in.Id <= 0 || in.Type == "" {
		return nil, status.Error(codes.InvalidArgument, "invalid params")
	}
	// TODO: 缓存中是否存在
	// ...

	ql := `select count(1) from message_receivers where receiver_id=? and receiver_type=? and status=0 and (deleted is null or deleted=0)`
	d, err := l.svcCtx.DB.DB()
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	var count int64
	err = d.QueryRowContext(l.ctx, ql, in.Id, in.Type).Scan(&count)
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &iot_message.UnreadMessageCountResponse{Count: int32(count)}, nil
}
