package logic

import (
	"context"
	"time"

	"git.cdtwzn.com/iot/iot_message/internal/svc"
	"git.cdtwzn.com/iot/iot_message/iot_message"
	"git.cdtwzn.com/iot/iot_message/pkg/usertypes"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/zeromicro/go-zero/core/logx"
)

type MarkAllMessagesAsReadLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewMarkAllMessagesAsReadLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MarkAllMessagesAsReadLogic {
	return &MarkAllMessagesAsReadLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 用户消息全部标记为已读
func (l *MarkAllMessagesAsReadLogic) MarkAllMessagesAsRead(in *iot_message.Receiver) (*iot_message.Response, error) {
	if in.Id <= 0 {
		return nil, status.Error(codes.InvalidArgument, "参数错误")
	}
	// 检查是否是有效的用户类型
	if !usertypes.CheckUserType(in.Type) {
		return nil, status.Error(codes.InvalidArgument, "invalid user type")
	}
	q := l.svcCtx.Query
	_, err := q.MessageReceiver.WithContext(l.ctx).Where(
		q.MessageReceiver.ReceiverID.Eq(in.Id), q.MessageReceiver.ReceiverType.Eq(in.Type), q.MessageReceiver.Status.Eq(0),
	).Updates(map[string]interface{}{
		"status":    1,
		"readed_at": time.Now().Local(),
	})
	if err != nil {
		logx.Errorf("更新消息状态失败: %v", err)
		return nil, status.Error(codes.Internal, "数据库更新错误")
	}
	return &iot_message.Response{}, nil
}
