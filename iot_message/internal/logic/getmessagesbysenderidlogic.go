package logic

import (
	"context"
	"database/sql"
	"time"

	"git.cdtwzn.com/iot/iot_message/internal/svc"
	"git.cdtwzn.com/iot/iot_message/iot_message"
	"git.cdtwzn.com/iot/iot_message/pkg/usertypes"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMessagesBySenderIDLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetMessagesBySenderIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMessagesBySenderIDLogic {
	return &GetMessagesBySenderIDLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 根据发送者ID获取消息列表
func (l *GetMessagesBySenderIDLogic) GetMessagesBySenderID(in *iot_message.GetMessagesRequest) (*iot_message.GetMessagesResponse, error) {
	if in.Receiver == nil || in.Receiver.Id <= 0 {
		return nil, status.Error(codes.InvalidArgument, "invalid params")
	}
	if in.Page < 1 || in.PageSize < 1 {
		return nil, status.Error(codes.InvalidArgument, "page or pagesize err")
	}
	// 检查是否是有效的用户类型
	if !usertypes.CheckUserType(in.Receiver.Type) {
		return nil, status.Error(codes.InvalidArgument, "invalid user type")
	}
	d, err := l.svcCtx.DB.DB()
	if err != nil {
		return nil, status.Error(codes.Internal, "db error:"+err.Error())
	}
	// 查询数量
	cql := `
	select count(1)
	from messages m
	left join message_receivers r on m.id=r.message_id
	where m.sender_id=? and m.sender_type=? and (m.expired_at is null or m.expired_at>now()) and (r.deleted is null or r.deleted=0) 
	`
	cs := d.QueryRow(cql, in.Receiver.Id, in.Receiver.Type)
	var total int32
	if err := cs.Scan(&total); err != nil {
		logx.Errorf("get total err:%s", err.Error())
		return nil, status.Error(codes.Internal, "get total err")
	}
	ql := `
	select m.id,m.title,m.content,m.mtype,m.sender_id,m.created_at,m.expired_at,m.extra,
	       r.readed_at,r.status
	from messages m
	left join message_receivers r on m.id=r.message_id
	where m.sender_id=? and m.sender_type=? and (m.expired_at is null or m.expired_at>now()) and (r.deleted is null or r.deleted=0) 
	order by m.created_at limit ? offset ?
	`
	rs, err := d.Query(ql, in.Receiver.Id, in.Receiver.Type, in.PageSize, (in.Page-1)*in.PageSize)
	if err != nil {
		return nil, status.Error(codes.Internal, "db error:"+err.Error())
	}
	defer rs.Close()
	var messages []*iot_message.Message
	for rs.Next() {
		msg := &iot_message.Message{}
		var createdAt, expiredAt, readAt sql.NullTime
		if err := rs.Scan(&msg.Id, &msg.Title, &msg.Content, &msg.Mtype, &msg.SenderId,
			&createdAt, &expiredAt, &msg.Extra, &readAt, &msg.Status); err != nil {
			return nil, status.Error(codes.Internal, "db error:"+err.Error())
		}
		if createdAt.Valid {
			msg.CreatedAt = createdAt.Time.Format(time.DateTime)
		}
		if expiredAt.Valid {
			msg.ExpiredAt = expiredAt.Time.Format(time.DateTime)
		}
		if readAt.Valid {
			msg.ReadAt = readAt.Time.Format(time.DateTime)
		}
		messages = append(messages, msg)
	}
	return &iot_message.GetMessagesResponse{
		Messages: messages,
		Total:    total,
	}, nil
}
