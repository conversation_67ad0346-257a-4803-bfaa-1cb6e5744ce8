package logic

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"git.cdtwzn.com/iot/iot_message/internal/dao/model"
	"git.cdtwzn.com/iot/iot_message/internal/svc"
	"git.cdtwzn.com/iot/iot_message/iot_message"
	"git.cdtwzn.com/iot/iot_message/pkg/usertypes"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/bytedance/sonic"
	"github.com/zeromicro/go-zero/core/logx"
)

type SendMessageLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSendMessageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SendMessageLogic {
	return &SendMessageLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 发送一条消息
func (l *SendMessageLogic) SendMessage(in *iot_message.SendMessageRequest) (*iot_message.SendMessageResponse, error) {
	if in.Message == nil || in.Receivers == nil || len(in.Receivers) == 0 {
		return nil, status.Error(codes.InvalidArgument, "参数错误")
	}
	if in.Message.Title == "" && in.Message.Content == "" {
		return nil, status.Error(codes.InvalidArgument, "消息标题或内容不能为同时为空")
	}
	if in.Message.SenderType == "" || in.Message.SenderId == 0 {
		return nil, status.Error(codes.InvalidArgument, "发送者类型和ID不能为空")
	}
	// 校验发送者类型
	if !usertypes.CheckUserType(in.Message.SenderType) {
		return nil, status.Error(codes.InvalidArgument, "发送者类型不正确")
	}
	// 发送内容存储为JSON格式，方便扩展
	if !sonic.ValidString(in.Message.Content) {
		return nil, status.Error(codes.InvalidArgument, "消息内容格式不正确")
	}
	// 扩展字段存储为JSON格式，方便扩展
	if in.Message.Extra == "" {
		in.Message.Extra = "{}"
	}
	if !sonic.Valid([]byte(in.Message.Extra)) {
		return nil, status.Error(codes.InvalidArgument, "消息扩展字段格式不正确")
	}

	err := l.sendMessage(in)
	if err != nil {
		logx.Errorf("SendMessage error: %v", err)
		return nil, status.Error(codes.Internal, err.Error())
	}
	// TODO 发送通知
	// ...
	return &iot_message.SendMessageResponse{}, nil
}

func (l *SendMessageLogic) sendMessage(in *iot_message.SendMessageRequest) (err error) {
	q := l.svcCtx.Query.Begin()
	defer func() {
		if err != nil {
			if e := q.Rollback(); e != nil {
				err = status.Error(codes.Internal, errors.Join(err, e).Error())
			}
		} else {
			if e := q.Commit(); e != nil {
				err = status.Error(codes.Internal, errors.Join(err, e).Error())
			}
		}
	}()
	mm := &model.Message{
		Title:      in.Message.Title,
		Content:    in.Message.Content,
		Mtype:      int32(in.Message.Mtype),
		SenderType: in.Message.SenderType,
		SenderID:   in.Message.SenderId,
		Extra:      in.Message.Extra,
	}
	err = q.Message.WithContext(l.ctx).Create(mm)
	if err != nil {
		return
	}
	// 插入接收者
	count := 0
	for _, r := range in.Receivers {
		if r.Id == 0 || r.Type == "" {
			continue
		}
		// 校验接收者类型
		if !usertypes.CheckUserType(r.Type) {
			logx.Errorf("invalid receiver type: %s", r.Type)
			continue
		}
		count += 1
		mr := &model.MessageReceiver{
			MessageID:    mm.ID,
			ReceiverID:   r.Id,
			ReceiverType: r.Type,
			Status:       0,
			ReceivedAt:   sql.NullTime{Valid: true, Time: time.Now().Local()},
		}
		err = q.MessageReceiver.WithContext(l.ctx).Create(mr)
		if err != nil {
			return
		}
	}
	if count == 0 {
		err = status.Error(codes.InvalidArgument, "没有有效的接收者")
		return
	}
	return nil
}
