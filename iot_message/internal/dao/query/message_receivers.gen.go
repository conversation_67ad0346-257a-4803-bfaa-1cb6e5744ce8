// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.cdtwzn.com/iot/iot_message/internal/dao/model"
)

func newMessageReceiver(db *gorm.DB, opts ...gen.DOOption) messageReceiver {
	_messageReceiver := messageReceiver{}

	_messageReceiver.messageReceiverDo.UseDB(db, opts...)
	_messageReceiver.messageReceiverDo.UseModel(&model.MessageReceiver{})

	tableName := _messageReceiver.messageReceiverDo.TableName()
	_messageReceiver.ALL = field.NewAsterisk(tableName)
	_messageReceiver.ID = field.NewInt64(tableName, "id")
	_messageReceiver.MessageID = field.NewInt64(tableName, "message_id")
	_messageReceiver.ReceiverType = field.NewString(tableName, "receiver_type")
	_messageReceiver.ReceiverID = field.NewInt64(tableName, "receiver_id")
	_messageReceiver.Status = field.NewInt32(tableName, "status")
	_messageReceiver.ReadedAt = field.NewTime(tableName, "readed_at")
	_messageReceiver.Deleted = field.NewInt32(tableName, "deleted")
	_messageReceiver.ReceivedAt = field.NewTime(tableName, "received_at")

	_messageReceiver.fillFieldMap()

	return _messageReceiver
}

// messageReceiver 消息接收者表
type messageReceiver struct {
	messageReceiverDo messageReceiverDo

	ALL          field.Asterisk
	ID           field.Int64  // 自增ID
	MessageID    field.Int64  // 消息ID
	ReceiverType field.String // 接收者类型
	ReceiverID   field.Int64  // 接收者ID
	Status       field.Int32  // 状态: 0-未读, 1-已读, 2-删除
	ReadedAt     field.Time   // 阅读时间
	Deleted      field.Int32  // 删除标记: 0-未删除, 1-已删除
	ReceivedAt   field.Time   // 接收时间

	fieldMap map[string]field.Expr
}

func (m messageReceiver) Table(newTableName string) *messageReceiver {
	m.messageReceiverDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m messageReceiver) As(alias string) *messageReceiver {
	m.messageReceiverDo.DO = *(m.messageReceiverDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *messageReceiver) updateTableName(table string) *messageReceiver {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.MessageID = field.NewInt64(table, "message_id")
	m.ReceiverType = field.NewString(table, "receiver_type")
	m.ReceiverID = field.NewInt64(table, "receiver_id")
	m.Status = field.NewInt32(table, "status")
	m.ReadedAt = field.NewTime(table, "readed_at")
	m.Deleted = field.NewInt32(table, "deleted")
	m.ReceivedAt = field.NewTime(table, "received_at")

	m.fillFieldMap()

	return m
}

func (m *messageReceiver) WithContext(ctx context.Context) *messageReceiverDo {
	return m.messageReceiverDo.WithContext(ctx)
}

func (m messageReceiver) TableName() string { return m.messageReceiverDo.TableName() }

func (m messageReceiver) Alias() string { return m.messageReceiverDo.Alias() }

func (m messageReceiver) Columns(cols ...field.Expr) gen.Columns {
	return m.messageReceiverDo.Columns(cols...)
}

func (m *messageReceiver) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *messageReceiver) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["message_id"] = m.MessageID
	m.fieldMap["receiver_type"] = m.ReceiverType
	m.fieldMap["receiver_id"] = m.ReceiverID
	m.fieldMap["status"] = m.Status
	m.fieldMap["readed_at"] = m.ReadedAt
	m.fieldMap["deleted"] = m.Deleted
	m.fieldMap["received_at"] = m.ReceivedAt
}

func (m messageReceiver) clone(db *gorm.DB) messageReceiver {
	m.messageReceiverDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m messageReceiver) replaceDB(db *gorm.DB) messageReceiver {
	m.messageReceiverDo.ReplaceDB(db)
	return m
}

type messageReceiverDo struct{ gen.DO }

func (m messageReceiverDo) Debug() *messageReceiverDo {
	return m.withDO(m.DO.Debug())
}

func (m messageReceiverDo) WithContext(ctx context.Context) *messageReceiverDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m messageReceiverDo) ReadDB() *messageReceiverDo {
	return m.Clauses(dbresolver.Read)
}

func (m messageReceiverDo) WriteDB() *messageReceiverDo {
	return m.Clauses(dbresolver.Write)
}

func (m messageReceiverDo) Session(config *gorm.Session) *messageReceiverDo {
	return m.withDO(m.DO.Session(config))
}

func (m messageReceiverDo) Clauses(conds ...clause.Expression) *messageReceiverDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m messageReceiverDo) Returning(value interface{}, columns ...string) *messageReceiverDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m messageReceiverDo) Not(conds ...gen.Condition) *messageReceiverDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m messageReceiverDo) Or(conds ...gen.Condition) *messageReceiverDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m messageReceiverDo) Select(conds ...field.Expr) *messageReceiverDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m messageReceiverDo) Where(conds ...gen.Condition) *messageReceiverDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m messageReceiverDo) Order(conds ...field.Expr) *messageReceiverDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m messageReceiverDo) Distinct(cols ...field.Expr) *messageReceiverDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m messageReceiverDo) Omit(cols ...field.Expr) *messageReceiverDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m messageReceiverDo) Join(table schema.Tabler, on ...field.Expr) *messageReceiverDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m messageReceiverDo) LeftJoin(table schema.Tabler, on ...field.Expr) *messageReceiverDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m messageReceiverDo) RightJoin(table schema.Tabler, on ...field.Expr) *messageReceiverDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m messageReceiverDo) Group(cols ...field.Expr) *messageReceiverDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m messageReceiverDo) Having(conds ...gen.Condition) *messageReceiverDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m messageReceiverDo) Limit(limit int) *messageReceiverDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m messageReceiverDo) Offset(offset int) *messageReceiverDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m messageReceiverDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *messageReceiverDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m messageReceiverDo) Unscoped() *messageReceiverDo {
	return m.withDO(m.DO.Unscoped())
}

func (m messageReceiverDo) Create(values ...*model.MessageReceiver) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m messageReceiverDo) CreateInBatches(values []*model.MessageReceiver, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m messageReceiverDo) Save(values ...*model.MessageReceiver) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m messageReceiverDo) First() (*model.MessageReceiver, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MessageReceiver), nil
	}
}

func (m messageReceiverDo) Take() (*model.MessageReceiver, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MessageReceiver), nil
	}
}

func (m messageReceiverDo) Last() (*model.MessageReceiver, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MessageReceiver), nil
	}
}

func (m messageReceiverDo) Find() ([]*model.MessageReceiver, error) {
	result, err := m.DO.Find()
	return result.([]*model.MessageReceiver), err
}

func (m messageReceiverDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MessageReceiver, err error) {
	buf := make([]*model.MessageReceiver, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m messageReceiverDo) FindInBatches(result *[]*model.MessageReceiver, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m messageReceiverDo) Attrs(attrs ...field.AssignExpr) *messageReceiverDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m messageReceiverDo) Assign(attrs ...field.AssignExpr) *messageReceiverDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m messageReceiverDo) Joins(fields ...field.RelationField) *messageReceiverDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m messageReceiverDo) Preload(fields ...field.RelationField) *messageReceiverDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m messageReceiverDo) FirstOrInit() (*model.MessageReceiver, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MessageReceiver), nil
	}
}

func (m messageReceiverDo) FirstOrCreate() (*model.MessageReceiver, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MessageReceiver), nil
	}
}

func (m messageReceiverDo) FindByPage(offset int, limit int) (result []*model.MessageReceiver, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m messageReceiverDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m messageReceiverDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m messageReceiverDo) Delete(models ...*model.MessageReceiver) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *messageReceiverDo) withDO(do gen.Dao) *messageReceiverDo {
	m.DO = *do.(*gen.DO)
	return m
}
