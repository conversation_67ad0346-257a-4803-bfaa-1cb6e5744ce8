// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "database/sql"

const TableNameMessage = "messages"

// Message 消息表
type Message struct {
	ID         int64        `gorm:"column:id;primaryKey;autoIncrement:true;comment:自增ID" json:"id"`                               // 自增ID
	Title      string       `gorm:"column:title;not null;comment:消息标题" json:"title"`                                              // 消息标题
	Content    string       `gorm:"column:content;not null;comment:消息内容" json:"content"`                                          // 消息内容
	Mtype      int32        `gorm:"column:mtype;not null;comment:消息类型: 0-普通消息, 1-系统消息, 2-告警消息, 3-事件消息, ... , 10-其他" json:"mtype"` // 消息类型: 0-普通消息, 1-系统消息, 2-告警消息, 3-事件消息, ... , 10-其他
	SenderType string       `gorm:"column:sender_type;not null;comment:发送者类型" json:"sender_type"`                                 // 发送者类型
	SenderID   int64        `gorm:"column:sender_id;not null;comment:发送者ID" json:"sender_id"`                                     // 发送者ID
	CreatedAt  sql.NullTime `gorm:"column:created_at;not null;comment:创建时间" json:"created_at"`                                    // 创建时间
	ExpiredAt  sql.NullTime `gorm:"column:expired_at;comment:过期时间" json:"expired_at"`                                             // 过期时间
	Extra      string       `gorm:"column:extra;comment:附加信息" json:"extra"`                                                       // 附加信息
}

// TableName Message's table name
func (*Message) TableName() string {
	return TableNameMessage
}
