// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import "database/sql"

const TableNameMessageReceiver = "message_receivers"

// MessageReceiver 消息接收者表
type MessageReceiver struct {
	ID           int64        `gorm:"column:id;primaryKey;autoIncrement:true;comment:自增ID" json:"id"`   // 自增ID
	MessageID    int64        `gorm:"column:message_id;not null;comment:消息ID" json:"message_id"`        // 消息ID
	ReceiverType string       `gorm:"column:receiver_type;not null;comment:接收者类型" json:"receiver_type"` // 接收者类型
	ReceiverID   int64        `gorm:"column:receiver_id;not null;comment:接收者ID" json:"receiver_id"`     // 接收者ID
	Status       int32        `gorm:"column:status;comment:状态: 0-未读, 1-已读, 2-删除" json:"status"`         // 状态: 0-未读, 1-已读, 2-删除
	ReadedAt     sql.NullTime `gorm:"column:readed_at;comment:阅读时间" json:"readed_at"`                   // 阅读时间
	Deleted      int32        `gorm:"column:deleted;comment:删除标记: 0-未删除, 1-已删除" json:"deleted"`         // 删除标记: 0-未删除, 1-已删除
	ReceivedAt   sql.NullTime `gorm:"column:received_at;not null;comment:接收时间" json:"received_at"`      // 接收时间
}

// TableName MessageReceiver's table name
func (*MessageReceiver) TableName() string {
	return TableNameMessageReceiver
}
