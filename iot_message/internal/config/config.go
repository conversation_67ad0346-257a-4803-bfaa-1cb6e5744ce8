package config

import (
	"git.cdtwzn.com/iot/iot_common/config"
	"github.com/zeromicro/go-zero/zrpc"
)

type Config struct {
	zrpc.RpcServerConf
	DB              config.DatabaseConf `json:"DB" yaml:"DB"`                           // 数据库配置
	CustomerRpcConf *zrpc.RpcClientConf `json:"CustomerRpcConf" yaml:"CustomerRpcConf"` // Customer RPC client configuration
	AuthRpcConf     *zrpc.RpcClientConf `json:"AuthRpcConf" yaml:"AuthRpcConf"`         // Auth RPC client configuration
}
