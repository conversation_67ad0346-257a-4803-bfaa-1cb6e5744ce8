// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.1
// Source: iot_message.proto

package server

import (
	"context"

	"git.cdtwzn.com/iot/iot_message/internal/logic"
	"git.cdtwzn.com/iot/iot_message/internal/svc"
	"git.cdtwzn.com/iot/iot_message/iot_message"
)

type IotMessageServer struct {
	svcCtx *svc.ServiceContext
	iot_message.UnimplementedIotMessageServer
}

func NewIotMessageServer(svcCtx *svc.ServiceContext) *IotMessageServer {
	return &IotMessageServer{
		svcCtx: svcCtx,
	}
}

func (s *IotMessageServer) Ping(ctx context.Context, in *iot_message.Request) (*iot_message.Response, error) {
	l := logic.NewPingLogic(ctx, s.svcCtx)
	return l.<PERSON>(in)
}

// 发送一条消息
func (s *IotMessageServer) SendMessage(ctx context.Context, in *iot_message.SendMessageRequest) (*iot_message.SendMessageResponse, error) {
	l := logic.NewSendMessageLogic(ctx, s.svcCtx)
	return l.SendMessage(in)
}

// 获取消息详情(内嵌实现已读状态)
func (s *IotMessageServer) GetMessageDetail(ctx context.Context, in *iot_message.MessageDetailRequest) (*iot_message.Message, error) {
	l := logic.NewGetMessageDetailLogic(ctx, s.svcCtx)
	return l.GetMessageDetail(in)
}

// 根据用户ID获取消息列表
func (s *IotMessageServer) GetMessagesByUID(ctx context.Context, in *iot_message.GetMessagesRequest) (*iot_message.GetMessagesResponse, error) {
	l := logic.NewGetMessagesByUIDLogic(ctx, s.svcCtx)
	return l.GetMessagesByUID(in)
}

// 根据发送者ID获取消息列表
func (s *IotMessageServer) GetMessagesBySenderID(ctx context.Context, in *iot_message.GetMessagesRequest) (*iot_message.GetMessagesResponse, error) {
	l := logic.NewGetMessagesBySenderIDLogic(ctx, s.svcCtx)
	return l.GetMessagesBySenderID(in)
}

// 用户消息全部标记为已读
func (s *IotMessageServer) MarkAllMessagesAsRead(ctx context.Context, in *iot_message.Receiver) (*iot_message.Response, error) {
	l := logic.NewMarkAllMessagesAsReadLogic(ctx, s.svcCtx)
	return l.MarkAllMessagesAsRead(in)
}

// 删除一条消息
func (s *IotMessageServer) DeleteMessage(ctx context.Context, in *iot_message.MessageDeleteRequest) (*iot_message.Response, error) {
	l := logic.NewDeleteMessageLogic(ctx, s.svcCtx)
	return l.DeleteMessage(in)
}

// 根据UID获取用户未读消息数量
func (s *IotMessageServer) GetUnreadMessageCount(ctx context.Context, in *iot_message.Receiver) (*iot_message.UnreadMessageCountResponse, error) {
	l := logic.NewGetUnreadMessageCountLogic(ctx, s.svcCtx)
	return l.GetUnreadMessageCount(in)
}
