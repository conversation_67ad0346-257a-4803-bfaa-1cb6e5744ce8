package svc

import (
	"context"

	"git.cdtwzn.com/iot/iot_message/internal/config"
	"git.cdtwzn.com/iot/iot_message/internal/dao/query"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type ServiceContext struct {
	Config config.Config
	Redis  *redis.Redis

	Ctx       context.Context
	ctxCancel context.CancelFunc

	// NATS
	// Natsc *natsc.Conn

	// 远程日志
	// Rl iotlog.LogService

	// Database
	DB *gorm.DB

	// Model query
	Query *query.Query

	// 客户服务
	// customerAdminService customeradminservice.CustomerAdminService
}

func NewServiceContext(c config.Config) *ServiceContext {
	ctx, cc := context.WithCancel(context.Background())
	return &ServiceContext{
		Config:    c,
		Ctx:       ctx,
		ctxCancel: cc,
	}
}

func (s *ServiceContext) Init() error {

	s.Redis = s.Config.Redis.NewRedis()

	db, err := gorm.Open(mysql.Open(s.Config.DB.DataBaseDsn), &gorm.Config{})
	if err != nil {
		logx.Errorf("Failed to connect to database:%s|", s.Config.DB.DataBaseDsn)
		return err
	}
	s.DB = db

	s.Query = query.Use(s.DB)

	return nil
}

func (s *ServiceContext) Deinit() {
	if s.ctxCancel != nil {
		s.ctxCancel()
	}
	logx.Info("Service context deinitialized")
}
