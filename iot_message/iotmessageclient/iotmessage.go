// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.1
// Source: iot_message.proto

package iotmessageclient

import (
	"context"

	"git.cdtwzn.com/iot/iot_message/iot_message"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	GetMessagesRequest         = iot_message.GetMessagesRequest
	GetMessagesResponse        = iot_message.GetMessagesResponse
	Message                    = iot_message.Message
	MessageDeleteRequest       = iot_message.MessageDeleteRequest
	MessageDetailRequest       = iot_message.MessageDetailRequest
	Receiver                   = iot_message.Receiver
	Request                    = iot_message.Request
	Response                   = iot_message.Response
	SendMessageRequest         = iot_message.SendMessageRequest
	SendMessageResponse        = iot_message.SendMessageResponse
	Sender                     = iot_message.Sender
	UnreadMessageCountResponse = iot_message.UnreadMessageCountResponse

	IotMessage interface {
		Ping(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error)
		// 发送一条消息
		SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResponse, error)
		// 获取消息详情(内嵌实现已读状态)
		GetMessageDetail(ctx context.Context, in *MessageDetailRequest, opts ...grpc.CallOption) (*Message, error)
		// 根据用户ID获取消息列表
		GetMessagesByUID(ctx context.Context, in *GetMessagesRequest, opts ...grpc.CallOption) (*GetMessagesResponse, error)
		// 根据发送者ID获取消息列表
		GetMessagesBySenderID(ctx context.Context, in *GetMessagesRequest, opts ...grpc.CallOption) (*GetMessagesResponse, error)
		// 用户消息全部标记为已读
		MarkAllMessagesAsRead(ctx context.Context, in *Receiver, opts ...grpc.CallOption) (*Response, error)
		// 删除一条消息
		DeleteMessage(ctx context.Context, in *MessageDeleteRequest, opts ...grpc.CallOption) (*Response, error)
		// 根据UID获取用户未读消息数量
		GetUnreadMessageCount(ctx context.Context, in *Receiver, opts ...grpc.CallOption) (*UnreadMessageCountResponse, error)
	}

	defaultIotMessage struct {
		cli zrpc.Client
	}
)

func NewIotMessage(cli zrpc.Client) IotMessage {
	return &defaultIotMessage{
		cli: cli,
	}
}

func (m *defaultIotMessage) Ping(ctx context.Context, in *Request, opts ...grpc.CallOption) (*Response, error) {
	client := iot_message.NewIotMessageClient(m.cli.Conn())
	return client.Ping(ctx, in, opts...)
}

// 发送一条消息
func (m *defaultIotMessage) SendMessage(ctx context.Context, in *SendMessageRequest, opts ...grpc.CallOption) (*SendMessageResponse, error) {
	client := iot_message.NewIotMessageClient(m.cli.Conn())
	return client.SendMessage(ctx, in, opts...)
}

// 获取消息详情(内嵌实现已读状态)
func (m *defaultIotMessage) GetMessageDetail(ctx context.Context, in *MessageDetailRequest, opts ...grpc.CallOption) (*Message, error) {
	client := iot_message.NewIotMessageClient(m.cli.Conn())
	return client.GetMessageDetail(ctx, in, opts...)
}

// 根据用户ID获取消息列表
func (m *defaultIotMessage) GetMessagesByUID(ctx context.Context, in *GetMessagesRequest, opts ...grpc.CallOption) (*GetMessagesResponse, error) {
	client := iot_message.NewIotMessageClient(m.cli.Conn())
	return client.GetMessagesByUID(ctx, in, opts...)
}

// 根据发送者ID获取消息列表
func (m *defaultIotMessage) GetMessagesBySenderID(ctx context.Context, in *GetMessagesRequest, opts ...grpc.CallOption) (*GetMessagesResponse, error) {
	client := iot_message.NewIotMessageClient(m.cli.Conn())
	return client.GetMessagesBySenderID(ctx, in, opts...)
}

// 用户消息全部标记为已读
func (m *defaultIotMessage) MarkAllMessagesAsRead(ctx context.Context, in *Receiver, opts ...grpc.CallOption) (*Response, error) {
	client := iot_message.NewIotMessageClient(m.cli.Conn())
	return client.MarkAllMessagesAsRead(ctx, in, opts...)
}

// 删除一条消息
func (m *defaultIotMessage) DeleteMessage(ctx context.Context, in *MessageDeleteRequest, opts ...grpc.CallOption) (*Response, error) {
	client := iot_message.NewIotMessageClient(m.cli.Conn())
	return client.DeleteMessage(ctx, in, opts...)
}

// 根据UID获取用户未读消息数量
func (m *defaultIotMessage) GetUnreadMessageCount(ctx context.Context, in *Receiver, opts ...grpc.CallOption) (*UnreadMessageCountResponse, error) {
	client := iot_message.NewIotMessageClient(m.cli.Conn())
	return client.GetUnreadMessageCount(ctx, in, opts...)
}
